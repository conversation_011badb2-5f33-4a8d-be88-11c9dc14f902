import urllib.parse
'''
HOOK DEBUG


Function.prototype.temp_constructor= Function.prototype.constructor;
Function.prototype.constructor=function(){
if (arguments && typeof arguments[0]==="string"){
    if (arguments[0]==="debugger")
    return ""
}
return Function.prototype.temp_constructor.apply(this, arguments);
};

'''
'''
{
  "appId": "wb182564874663h2025068334",
  "b": "48e32fa0717e8f8748e18ce",
  "c": "a8c32b703134a8c88a0069c",
  "ci": 15,
  "co": 810,
  "sm": "白龙懒懒地盘在寒玉石上，它让元曜去后院的",
  "pr": 5,
  "rt": 30,
  "ts": 1726711643121,
  "rn": 932,
  "sg": "3ba339f4b9e29404985b4f627a2cd3e4054b9343a495e36f8ae92ce64c92b1f4",
  "ct": 1726711643,
  "ps": "12b32b207a4ac083g019ba1",
  "pc": "648324a07a4ac084g012694",
  "s": "3298fe4f"
}
{
  "appId": "",
  "b": "48e32fa0717e8f8748e18ce", # bookId
  "c": "a8c32b703134a8c88a0069c", # chapterUid
  "ci": 15, #chapterIdx
  "co": 810, #chapterOffset
  "sm": "白龙懒懒地盘在寒玉石上，它让元曜去后院的", #每页开头简述
  "pr": 5,
  "rt": 30,
  "ts": 1726711643121,
  "rn": 932, #1000以内的随机数
  "sg": "3ba339f4b9e29404985b4f627a2cd3e4054b9343a495e36f8ae92ce64c92b1f4",
  "ct": 1726711643,
  "ps": "12b32b207a4ac083g019ba1",
  "pc": "648324a07a4ac084g012694",
  "s": "3298fe4f"
}
'''

def sha256_encrypt(text):
    # 创建一个sha256哈希对象
    hash_object = hashlib.sha256()

    # 将文本编码为字节，然后更新哈希对象
    hash_object.update(text.encode('utf-8'))

    # 获取十六进制格式的哈希值
    hex_dig = hash_object.hexdigest()

    return hex_dig

#"appId=wb182564874663h2025068334&b=48e32fa0717e8f8748e18ce&c=a8c32b703134a8c88a0069c&ci=15&co=3553&ct=1726712042&pc=648324a07a4ac084g012694&pr=5&ps=12b32b207a4ac083g019ba1&rn=815&rt=30&sg=ff9fe12c24963785877dc9765f8383d1f2cf3ae65f8018de58a29ec9d004ccbc&sm=%E5%9B%A0%E4%B8%BA%E5%9C%A8%E5%B8%AD%E9%97%B4%E5%90%83%E5%BE%97%E5%A4%AA%E9%A5%B1%EF%BC%8C%E5%85%83%E6%9B%9C%E5%9D%90%E4%BA%86%E4%B8%80%E4%BC%9A%E5%84%BF%EF%BC%8C%E8%BF%98%E6%98%AF&ts=1726712039953"
def build_query_string(params, include_keys=None):
    """
    将字典 `params` 转换为 URL 查询字符串形式。如果 `include_keys` 不为空，则只包含在 `include_keys` 中的键。

    参数:
    - params: 要转换的字典对象
    - include_keys: 可选参数，指定要包含的键，默认为空列表（包含所有键）

    返回:
    - 生成的 URL 查询字符串
    """
    # 如果没有传入 include_keys，则初始化为空列表，表示包含所有键
    if include_keys is None:
        include_keys = []

    query_string = ''
    # 判断是否忽略 include_keys（如果为空，则包含所有键）
    include_all_keys = len(include_keys) == 0

    # 对字典的键进行排序
    sorted_keys = sorted(params.keys())

    # 遍历排序后的键
    for key in sorted_keys:
        # 如果 include_keys 为空或当前键在 include_keys 中，则处理该键值对
        if include_all_keys or key in include_keys:
            value = params[key]
            # 对键和值进行 URL 编码，并拼接成查询字符串格式
            query_string += f"{key}={urllib.parse.quote(str(value))}&"

    # 去掉末尾多余的 '&'
    if query_string.endswith('&'):
        query_string = query_string[:-1]

    return query_string


#"s": "7762ddd8"
def calculate_custom_hash(input_str):
    # 初始化哈希变量，使用十六进制常数 0x15051505
    hash1 = 0x15051505
    hash2 = hash1

    # 获取输入字符串的长度
    input_length = len(input_str)

    # 设置循环的起始索引为字符串长度减一（最后一个字符的索引）
    index = input_length - 1

    # 从字符串末尾开始，每次处理两个字符，步长为2
    while index > 0:
        # 获取当前字符的 Unicode 编码，并进行位移和异或操作
        hash1 = 0x7fffffff & (hash1 ^ (ord(input_str[index]) << ((input_length - index) % 30)))

        # 获取前一个字符的 Unicode 编码，并进行位移和异或操作
        hash2 = 0x7fffffff & (hash2 ^ (ord(input_str[index - 1]) << (index % 30)))

        # 递减索引，步长为2
        index -= 2

    # 将两个哈希值相加，并转换为十六进制字符串，最后转为小写
    return hex(hash1 + hash2)[2:].lower()

import hashlib

def process_input(input_value):
    # 如果输入是数字，转换为字符串；如果不是字符串，直接返回输入
    if isinstance(input_value, int):
        input_value = str(input_value)
    if not isinstance(input_value, str):
        return input_value

    # 计算输入字符串的 MD5 哈希值，并取前3个字符作为初始部分
    md5_hash = hashlib.md5(input_value.encode()).hexdigest()
    result_string = md5_hash[:3]

    # 定义一个内部函数，处理字符串中的数字字符和非数字字符
    def process_value(input_str):
        # 如果输入是纯数字字符
        if input_str.isdigit():
            input_length = len(input_str)
            hex_chunks = []

            # 将输入字符串按 9 个字符一组进行切分，并转换为十六进制
            for index in range(0, input_length, 9):
                chunk = input_str[index:index + 9]
                hex_chunks.append(hex(int(chunk))[2:])
            return '3', hex_chunks  # 返回前缀 '3' 和转换后的数组

        # 如果输入包含非数字字符，按字符编码转换为十六进制
        hex_string = ''.join([hex(ord(c))[2:] for c in input_str])
        return '4', [hex_string]  # 返回前缀 '4' 和转换后的十六进制字符串

    processed_value = process_value(input_value)

    # 拼接前缀和两位哈希
    result_string += processed_value[0]
    result_string += '2' + md5_hash[-2:]

    # 遍历转换后的十六进制片段，并依次拼接
    hex_array = processed_value[1]
    for i, hex_chunk in enumerate(hex_array):
        chunk_length_hex = hex(len(hex_chunk))[2:]
        if len(chunk_length_hex) == 1:
            chunk_length_hex = '0' + chunk_length_hex
        result_string += chunk_length_hex
        result_string += hex_chunk

        # 在每组之间加 'g' 作为分隔符
        if i < len(hex_array) - 1:
            result_string += 'g'

    # 如果拼接后的字符串长度不足 20 位，用 MD5 哈希值补齐
    if len(result_string) < 20:
        result_string += md5_hash[:20 - len(result_string)]

    # 最后再进行一次 MD5 哈希，并取前 3 位
    result_string += hashlib.md5(result_string.encode()).hexdigest()[:3]

    # 返回最终拼接的字符串
    return result_string




# 测试输入值 308
# process_input(25071495)


#  <script nonce="F556D25500F0B37D3A7E473DD9F20EC9">
#             window.__INITIAL_STATE__ = {
#                 "OS": "Windows",
#     }
# </script>
'''1. 获取cookie header'''
import requests
from bs4 import BeautifulSoup
import json
session = requests.Session()
cookie = "RK=63WgIZindX; ptcz=d833679f90ed3c0f3064db750c34057a2a246bf40e0d04186e2e3079ecee697c; pgv_pvid=3379984425; eas_sid=R1w7g15177Q3i4T6n047b599i7; qq_domain_video_guid_verify=be6aab3bb160b0d8; _qimei_uuid42=1840316032110079931eabef00e42d42e1ef286fc7; _qimei_fingerprint=373c5bebdee985a6979a0fde94db292e; _qimei_q36=; _qimei_h38=8f952340931eabef00e42d420200000f018403; ied_qq=o1657098617; _t_qbtool_uid=aaaazk65p6f7gqkhz2x7mnkcx75w88cb; _ga=GA1.1.205396055.1713447026; _ga_TPFW0KPXC1=GS1.1.1713600696.2.1.1713600761.0.0.0; LW_uid=V1f721S376l3L2S8c4L8B0o9Q4; pac_uid=0_zQnWrQsYXQ0Yh; LW_sid=r1q7N129j7v5W8g8a5v7Z126p3; suid=0_zQnWrQsYXQ0Yh; wr_vid=299244246; wr_pf=0; wr_rt=web%40JfN6vKZje616_O2KAis_AL; wr_localvid=05332750811d61ad6053809; wr_name=%E6%AC%A2%E4%B9%90%E9%A9%AC; wr_avatar=https%3A%2F%2Fthirdwx.qlogo.cn%2Fmmopen%2Fvi_32%2FQ3auHgzwzM6agn7maDc7vfzr2T7jUuUm7qU5b0seRP30C6Z2ZqD5RABIHf2RR9icz4xtZoXDKwe3seH7HJYRryg%2F132; wr_gender=1; wr_fp=151305756; wr_skey=0gu0GvOh"
headers = {
    "Cookie": "RK=63WgIZindX; ptcz=d833679f90ed3c0f3064db750c34057a2a246bf40e0d04186e2e3079ecee697c; pgv_pvid=3379984425; eas_sid=R1w7g15177Q3i4T6n047b599i7; qq_domain_video_guid_verify=be6aab3bb160b0d8; _qimei_uuid42=1840316032110079931eabef00e42d42e1ef286fc7; _qimei_fingerprint=373c5bebdee985a6979a0fde94db292e; _qimei_q36=; _qimei_h38=8f952340931eabef00e42d420200000f018403; ied_qq=o1657098617; _t_qbtool_uid=aaaazk65p6f7gqkhz2x7mnkcx75w88cb; _ga=GA1.1.205396055.1713447026; _ga_TPFW0KPXC1=GS1.1.1713600696.2.1.1713600761.0.0.0; LW_uid=V1f721S376l3L2S8c4L8B0o9Q4; pac_uid=0_zQnWrQsYXQ0Yh; LW_sid=r1q7N129j7v5W8g8a5v7Z126p3; suid=0_zQnWrQsYXQ0Yh; wr_vid=299244246; wr_pf=0; wr_rt=web%40JfN6vKZje616_O2KAis_AL; wr_localvid=05332750811d61ad6053809; wr_name=%E6%AC%A2%E4%B9%90%E9%A9%AC; wr_avatar=https%3A%2F%2Fthirdwx.qlogo.cn%2Fmmopen%2Fvi_32%2FQ3auHgzwzM6agn7maDc7vfzr2T7jUuUm7qU5b0seRP30C6Z2ZqD5RABIHf2RR9icz4xtZoXDKwe3seH7HJYRryg%2F132; wr_gender=1; wr_fp=151305756; wr_skey=0gu0GvOh",
    "sec-ch-ua-platform": "Windows",
    "sec-fetch-dest": "document",
    "sec-fetch-mode": "navigate",
    "sec-fetch-site": "same-origin",
    "upgrade-insecure-requests": "1",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
}

'''2. 请求 https://weread.qq.com/web/reader/48e32fa0717e8f8748e18ce'''
#  <script nonce="F556D25500F0B37D3A7E473DD9F20EC9">
#             window.__INITIAL_STATE__ = {
#                 "OS": "Windows",
#     };
#  (function() {
#     var s;
#     (s = document.currentScript || document.scripts[document.scripts.length - 1]).parentNode.removeChild(s);
# }());
# </script>
# 获取 window.__INITIAL_STATE__ 对象的内容
url = "https://weread.qq.com/web/reader/48e32fa0717e8f8748e18ce"
response = session.get(url, headers=headers)

# 解析HTML内容
soup = BeautifulSoup(response.text, 'html.parser')

# 查找包含 `window.__INITIAL_STATE__` 的脚本标签
script_tag = soup.find('script', string=lambda text: text and 'window.__INITIAL_STATE__' in text)

# 提取并解析 JSON 内容
import re
initial_state_str = re.sub(r'\(function\(.*$', '', script_tag.string, flags=re.S)
initial_state_str = initial_state_str.strip().split('=', 1)[1].strip()[:-1]  # 去除字符串两边的空格，并去掉最后的分号
initial_state = json.loads(initial_state_str)
token = initial_state['reader']['token']
#print(initial_state)
#print(initial_state['reader']['token'])

'''3.  阅读记录 https://weread.qq.com/web/book/read'''
'''
{
  "appId": "wb182564874663h2025068334",
  "b": "48e32fa0717e8f8748e18ce",
  "c": "a8c32b703134a8c88a0069c",
  "ci": 15,
  "co": 810,
  "sm": "白龙懒懒地盘在寒玉石上，它让元曜去后院的",
  "pr": 5,
  "rt": 30,
  "ts": 1726711643121,
  "rn": 932,
  "sg": "3ba339f4b9e29404985b4f627a2cd3e4054b9343a495e36f8ae92ce64c92b1f4",
  "ct": 1726711643,
  "ps": "12b32b207a4ac083g019ba1",
  "pc": "648324a07a4ac084g012694",
  "s": "3298fe4f"
}
'''
demo = {
  "appId": "wb182564874663h2025068334",
  "b": "48e32fa0717e8f8748e18ce",
  "c": "a8c32b703134a8c88a0069c",
  "ci": 15,
  "co": 810,
  "sm": "白龙懒懒地盘在寒玉石上，它让元曜去后院的",
  "pr": 5,
  "rt": 30,
  "ts": 1726711643121,
  "rn": 932,
  "sg": "3ba339f4b9e29404985b4f627a2cd3e4054b9343a495e36f8ae92ce64c92b1f4",
  "ct": 1726711643,
  "ps": "12b32b207a4ac083g019ba1",
  "pc": "648324a07a4ac084g012694",
}
#    "s": "3298fe4f"
import time
import random
tss = time.time()
ct = int(tss)
ts = int(tss * 1000)
rn = random.randint(100, 1000)
sg = sha256_encrypt("17267116431219323c5c8717f3daf09iop3423zafeqoi") #sha256(ts+rn+token)
s = calculate_custom_hash(build_query_string(demo))
url = "https://weread.qq.com/web/book/read"
data = {
    "appId": "wb182564874663h2025068334", #appid
    "b": process_input(initial_state['reader']['progress']['bookId']), #bookid
    "c": process_input(initial_state['reader']['progress']['book']['chapterUid']), #chapterid
    "ci": initial_state['reader']['currentChapter']['chapterIdx'], #chapteridx
    "co": 8959, #chapteroffset
    "ct": ct, #当前时间戳10位
    "sm": "白龙懒懒地盘在寒玉石上，它让元曜去后院的", #每页开头简述
    "pr": 5, #progress
    "rt": random.randint(20, 100), #阅读时间
    "ts": ts, #当前时间戳13位
    "rn": rn, #1000以内随机整数
    "sg": sha256_encrypt(f"{ts}{rn}{token}"), #sha256(ts+rn+token)
    "ps": "12b32b207a4ac083g019ba1",
    "pc": "648324a07a4ac084g012694",
}
data.setdefault("s", calculate_custom_hash(build_query_string(data)))
url = "https://weread.qq.com/web/book/read"
res = session.post(url, data=data)
pass
