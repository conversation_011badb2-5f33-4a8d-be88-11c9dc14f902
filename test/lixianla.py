import requests

import requests

url = "https://lixianla.com/sg_sign-lx-1167750819.htm"

payload = {
  'vcode': "33576"
}

headers = {
  'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0",
  'sec-ch-ua': "\"Microsoft Edge\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
  'referer': "https://lixianla.com/index-2.htm",
  'accept-language': "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
  'Cookie': "bbs_sid=h44b25ua3h0tcqb9g2elp73rue; bbs_token=kxMTUReoorB_2BLRYC0GKsFh5BOphScLDArfH8ktxqGfxQLM79oBBX1JcXf3Umy_2FNvRITGvanVZ9_2FHuc3k3_2FMB0oCPI70_3D"
}

response = requests.post(url, data=payload, headers=headers, verify=False)

#print(response.text)

from bs4 import BeautifulSoup

soup = BeautifulSoup(response.text, "html.parser")
tag = soup.find('h4', class_='card-title text-center mb-0')
print(tag.text.strip())

