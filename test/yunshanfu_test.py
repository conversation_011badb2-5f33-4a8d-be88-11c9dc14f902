import random
import string



# 示例：生成一个默认长度为16的随机字符串
print()
# {
#   "timestamp": 1729786067253,
#   "nonceStr": "Y8J5oSTCkvL7nxd82Nqy",
#   "sign": "cec8f7fcf7f17c87dd128f357d5eb970"
# }
import time
import hashlib


class YSFCrypt:
    salt = "duiba123123"
    def get_sign_data(data=None, salt=None, random_str=None, timestamp=None):
        """
        计算连接字符串的 MD5 哈希值

        参数:
        salt (str): 第一个字符串
        random_str (str): 第二个字符串
        timestamp (str): 第三个字符串

        返回:
        str: 计算得到的 MD5 哈希值的十六进制表示
        """
        salt = salt if salt else YSFCrypt.salt
        random_str = random_str if random_str else YSFCrypt.random_str()
        timestamp = timestamp if timestamp else int(time.time() * 1000)
        if data:
            # 如果 data 存在，将其 JSON 序列化后与 salt, random_str, timestamp, 拼接，计算 MD5 哈希
            data_str = json.dumps(data, separators=(',', ':'))  # JSON序列化，去掉多余空白
            combined_str = f"{salt}{data_str}{random_str}{timestamp}"
            sign = hashlib.md5(combined_str.encode()).hexdigest()

            # 更新 s.data 的内容，包含签名、时间戳和随机字符串
            return {
                "data": data_str,
                "timestamp": timestamp,
                "nonceStr": random_str,
                "sign": sign
            }
        elif not data:
            # 如果 data 不存在，拼接 salt, random_str, timestamp 生成签名
            combined_str = f"{salt}{random_str}{timestamp}"
            sign = hashlib.md5(combined_str.encode()).hexdigest()
            # 更新 s.data 为包含签名、时间戳和随机字符串
            return {
                "timestamp": timestamp,
                "nonceStr": random_str,
                "sign": sign
            }
    def random_str(s=16):
        """
        生成一个随机字符串

        参数:
        s (int): 生成的随机字符串长度，默认为16

        返回:
        str: 生成的随机字符串
        """
        # 定义可用字符，包括大写字母、小写字母和数字
        characters = string.ascii_uppercase + string.ascii_lowercase + string.digits
        # 生成随机字符串
        result = ''.join(random.choice(characters) for _ in range(s))
        return result

# 示例：传入三个字符串
#print(YSFCrypt.md5_hash(j, _, o))
import requests,json
session = requests.session()
url = f"https://yxq.95516.com/taskCenter/api/app/sign/index"
salt = "duiba123123"
timestamp = int(time.time() * 1000)
random_str = YSFCrypt.random_str()
data = YSFCrypt.get_sign_data()
payload = json.dumps(data)
headers = {
    'Content-Type': "application/json",
    'Cookie': "_tracker_distinct_id_=20241023d59e59c5; canvasId=%5Bobject%20Object%5D; _tracker_session_id_=7836de92-0f13-416b-8561-d19afb7e3c56; _tracker_launch_=1; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22FRGlBmWFAPJygXsPD2tdhJJFDFhf1Gbo93OeS1R9Xt8TXIUBL%2Bco6Lo6knBnNZNR%22%2C%22first_id%22%3A%22192b7e338d925d-08dcac363853b8-3139456a-376980-192b7e338daac9%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTkyYjdlMzM4ZDkyNWQtMDhkY2FjMzYzODUzYjgtMzEzOTQ1NmEtMzc2OTgwLTE5MmI3ZTMzOGRhYWM5In0%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A%22%22%7D%2C%22%24device_id%22%3A%22192b7e338d925d-08dcac363853b8-3139456a-376980-192b7e338daac9%22%7D; cuk=wSWQiOiIxIiwic2leyJjZXJ0VHlwZSI6IjAxIiwiY29uc3VtZXJJZCI6IjUxMTIzOTIiLCJvcGVuSWQiOiJYVmJNTzhpQTRCVG5aSjcrTndtY21Fa3NkSDhubHVOZjVvaG82VjlZcUd1ODJuWW5Vb0MwVjR0d09vYWd1UE5vIiwiYXBwSWQiOiJjMWYyY2VhYTYwMGM0MWVhODEzODc2MjA3YTUxMWY5YiIsImNoYW5uZWwiOiJ1X2FwcCIsInNpZ24iOiI0YThmMTY3NWM3MzVjNTI0ZTliZGI2MTY1OTg3N2NkOCIsImVuY0NlcnRJZCI6IjNhMDdjNTU4NzQyZGQ2ZWU2NDBmNGFjY2UxMzJhNjQ4OGVlM2ZlMmYwMmJiMGE3ZDZmOTA4ODY2MThmYmI5NWMiLCJjZXJ0SWQiOiJmZTQ2NzgyOTE4MTljZjJiYzk0YWNlMzc3NzRhY2RlM2Q0MzAxM2NhNGFjNDM4OTExY2Y3ZDQ5YmRlYTU3MDgzIiwidXNlcklkIjoiYzAwNTM1MjA1NzE3IiwidGltZXN0YW1wIjoiMTcyOTc4MTYwODYzMyJ9"
}
session.headers = headers
response = session.post(url, data=payload)
data = response.json()
todo_tasks = [task for task in data["data"] if not (lambda task: task["taskId"] == 19 or task['taskStatus'] == 2)(task)]
todo_tasks = [task for task in data["data"] if (lambda task: task["taskId"] == 39)(task)]
print(f"共{len(todo_tasks)}个任务")
reward_count = 0
reward_sum = 0
for task in todo_tasks:
    print(f"去做任务：{task['taskTitle']}")
    url = "https://yxq.95516.com/taskCenter/api/app/sign/doCompleted"
    payload = json.dumps({
    "data": "{\"taskId\":41}",
    "timestamp": 1729791206625,
    "nonceStr": "Qhfn0TIAAL6o2gTfD7im",
    "sign": "2ba3cb7f02ba18a9cfeb361a832b18f9"
    })
    sign_data = YSFCrypt.get_sign_data({"taskId": task["taskId"]})
    payload = json.dumps(sign_data)
    response = requests.post(url, data=payload, headers=headers)
    data = response.json()
    if data.get("ok"):
        print(f"任务完成：{task['taskTitle']}")
    else:
        print(f"任务失败：{data['msg']}")
    url = "https://yxq.95516.com/taskCenter/api/app/task/sendPrize"

    payload = json.dumps({
    "data": "{\"taskId\":41}",
    "timestamp": 1729791215193,
    "nonceStr": "yCYB57veRHKC00xbQ8qj",
    "sign": "e4ff923281316002a6646babe894fce7"
    })
    sign_data = YSFCrypt.get_sign_data({"taskId": task["taskId"]})
    payload = json.dumps(sign_data)
    response = requests.post(url, data=payload, headers=headers)
    data = response.json()
    if data.get("ok"):

        print(f"奖励成功：{data['data']['prizeName']}")
        reward_count += 1
        reward_sum += data['data']['pointPrizeInfo']['pointAt']
    else:
        print(f"任务失败：{data['msg']}")


pass
