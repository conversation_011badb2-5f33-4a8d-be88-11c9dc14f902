#将下面代码转为 Python 实现，同时加上中文注释，修改变量命名使其更可读
import requests
import time
# url = "https://www.duokan.com/hs/v4/channel/query/588"

# params = {
#   'user_type': "3",
#   'fetch_pos': "head"
# }
# device_id = "D006300c65b1b4bee4ab7d934b18e0cbebbd763"
def get_sign_data():
    device_id = "D006300c65b1b4bee4ab7d934b18e0cbebbd763"
    t = int(time.time())
    t_device_id = f"{device_id}&{t}"
    c = 0
    for one in t_device_id:
        c = (c * 131 + ord(one)) % 65536
    return {"_t": t, "_c": c}

# payload = {
#   'features': "{}",
#   'withid': "1"
# }
# payload.update(get_sign_data())

# headers = {
#   'User-Agent': "Mozilla/5.0 (Linux; Android 15; 23013RK75C Build/AQ3A.240912.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/131.0.6778.201 Mobile Safari/537.36 XiaoMi/MiuiBrowser/2.1.1",
#   'Cookie': "app_id=DuoKan; channel=Y6WBAG; api=2; fiction_level=0_1; _m=1; book_level=0_1; random_id=ec69eb0843322403c538939a25176463; first_version=741070000; isYouthMode=false; login_user_id=d2da63da3cb61c61c543637f34286cf7; new_continue_month_strategy=1; phone=23013RK75C; lang=zh_CN; personal_recommend=1; store_pref=publish; personalise_ad=1; fresh_install_time=1720120988468; platform=android; ch=Y6WBAG; hidden_channels=recommend%2Caudio%2Cfemale%2Cmale; max_book_version=2; phoneModel=23013RK75C; osVersion=Android%20; imei=; xiaoMiPhone=true; serviceToken=bl/u3NYyV7kZCdzM921xGJ3hdE5+/pW46B1IX00YL72DDBxbAtmdSfIcW7uaswrYpJP1W/5z8OitHaP0qm14QtHRgsn/1Rr5ymBsHqb1w1CG5bJI8wTkCB0DwS8JmnlJOJKeuxFnTqebbhe3FRNr2Bl/6GeLjwhZ8qf9y4YF1zk=%02%02M%0Akh%EF%BF%BD%EF%BF%BD; nickname=%E5%A4%8F%E5%87%89; iconUrl=https://cdn.cnbj1.fds.api.mi-img.com/user-avatar/p015qZyibtfy/IqTUG6xliJpzNQ_320.jpg; signature=; last_login=MI_LOCAL; miui=1; super_user=0; device_info=phone; fe_version=5.0.0; free=0; user_mode=0; most_prefer=1; icon_url=https%3A%2F%2Fcdn.cnbj1.fds.api.mi-img.com%2Fuser-avatar%2Fp015qZyibtfy%2FIqTUG6xliJpzNQ_320.jpg; dark_theme=1; build=744090000; version_name=7.44.9; sys_version=15; sys_version_code=35; isDark=1; user_gender=0; client_time=1737046398639; v=; android_id_hash=; p=DKec69eb0843322403c538939a25176463; user_type=2; reg_id=J37kvaHzT3YSFQZ3ZPcEdoeN97Hw0XtYxzNYTSJqR4I%3D; device_id=D006300c65b1b4bee4ab7d934b18e0cbebbd763; oaid=ef77c58f82121928; token=0v9Y6lI8BhVy4mk7N3HtOhc5fU5-AarCvX8_OKu-nDAIMQKGAKr9REM-B9kJCDVV4JGCkpZiOfMp1zn58yOTJQ.."
# }

# response = requests.post(url, params=params, data=payload, headers=headers)
# free_book_data = response.json()
# #print(free_book_data)

# book_title = free_book_data['items'][1]['data']['data'][0]['title']
# book_id = free_book_data['items'][1]['data']['data'][0]['fiction_id']
# url = "https://www.duokan.com/sync/progress/upload"

# payload = {
#   'book_id': "758270",
#   'book_name': "我有一身被动技.EPUB",
#   'duokan': "1",
#   'percent': "0",
#   'time': "47",
#   'word_count': "-546",
#   'data': "{\"SpecVersion\":\"2.0\",\"DeviceID\":\"D006300c65b1b4bee4ab7d934b18e0cbebbd763\",\"KernelVersion\":\"5.0.0.d8a2110\",\"BookID\":\"758270\",\"BookRevision\":\"\",\"Item\":{\"Type\":\"PROGRESS\",\"DataID\":\"0\",\"CreateTime\":\"1737046824\",\"LastModifyTime\":\"1737046824\",\"RefPos\":[\"0\",0,0,-1,0]}}",
# }
# data = {
#   "SpecVersion": "2.0",
#   "DeviceID": device_id,
#   "KernelVersion": "5.0.0.d8a2110",
#   "BookID": book_id,
#   "BookRevision": "",
#   "Item": {
#     "Type": "PROGRESS",
#     "DataID": "0",
#     "CreateTime": "1737046824",
#     "LastModifyTime": "1737046824",
#     "RefPos": ["0", 0, 0, -1, 0]
#   }
# }
# import json
# data_str = json.dumps(data,separators=[",", ":"])
# payload = {
#   'book_id': book_id,
#   'book_name': f"{book_title}.EPUB",
#   'duokan': "1",
#   'percent': "0",
#   'time': "600",
#   'word_count': "-546",
#   'data': data_str,
# }
# payload.update(get_sign_data())
# headers = {
#   'User-Agent': "Dalvik/2.1.0 (Linux; U; Android 15; 23013RK75C Build/AQ3A.240912.001)",
#   'Accept-Encoding': "gzip,deflate",
#   # 'business_code_key': "fe69582e-9b13-447a-a245-bf1601457559",
#   'Cookie': "osVersionIncremental=OS2.0.3.0.VMNCNXM;mi_version=OS2.0.3.0.VMNCNXM;_m=1;platform=android;app_id=DuoKan;build=744090000;channel=Y6WBAG;first_version=741070000;version_name=7.44.9;device_model=23013RK75C;device_name=mondrian;os_version=15;os_sdk=35;manufacturer=Xiaomi;random_id=ec69eb0843322403c538939a25176463;reg_id=J37kvaHzT3YSFQZ3ZPcEdoeN97Hw0XtYxzNYTSJqR4I%3D;personal_recommend=1;personalise_rec=1;store_pref=publish;user_type=1;book_level=0_1;fiction_level=0_1;appOpenChannel=;_t=1737046825;_c=5619;p=DKec69eb0843322403c538939a25176463;token=0v9Y6lI8BhVy4mk7N3HtOhc5fU5-AarCvX8_OKu-nDAIMQKGAKr9REM-B9kJCDVV4JGCkpZiOfMp1zn58yOTJQ..;"
# }

# response = requests.post(url, data=payload, headers=headers)
# res = response.json()
#print(res)
import requests

url = "https://www.duokan.com/growth/user/task/list"

payload = {
  'activity_id': "1138",
  'all_required': "1",
}
payload.update(get_sign_data())
headers = {
  'User-Agent': "Mozilla/5.0 (Linux; Android 15; 23013RK75C Build/AQ3A.240912.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/131.0.6778.201 Mobile Safari/537.36 XiaoMi/MiuiBrowser/2.1.1",
  'Accept': "application/json, text/plain, */*",
  'Accept-Encoding': "gzip, deflate, br, zstd",
  'traceparent': "00-d4570654b71b6e683e7217516d83dee7-4eae66a7c960251a-01",
  'sec-ch-ua-platform': "\"Android\"",
  'sec-ch-ua': "\"Android WebView\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"",
  'sec-ch-ua-mobile': "?1",
  'origin': "https://www.duokan.com",
  'x-requested-with': "com.duokan.reader",
  'sec-fetch-site': "same-origin",
  'sec-fetch-mode': "cors",
  'sec-fetch-dest': "empty",
  'referer': "https://www.duokan.com/app/checkIn?from=%E6%88%91%E7%9A%84%E9%A1%B5%E7%AD%BE%E5%88%B0%E5%85%A5%E5%8F%A3",
  'accept-language': "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
  'priority': "u=1, i",
  'Cookie': "app_id=DuoKan; channel=Y6WBAG; api=2; fiction_level=0_1; _m=1; book_level=0_1; random_id=ec69eb0843322403c538939a25176463; first_version=741070000; isYouthMode=false; login_user_id=d2da63da3cb61c61c543637f34286cf7; new_continue_month_strategy=1; phone=23013RK75C; lang=zh_CN; personal_recommend=1; store_pref=publish; personalise_ad=1; fresh_install_time=1720120988468; platform=android; ch=Y6WBAG; hidden_channels=recommend%2Caudio%2Cfemale%2Cmale; max_book_version=2; phoneModel=23013RK75C; osVersion=Android%20; imei=; xiaoMiPhone=true; serviceToken=bl/u3NYyV7kZCdzM921xGJ3hdE5+/pW46B1IX00YL72DDBxbAtmdSfIcW7uaswrYpJP1W/5z8OitHaP0qm14QtHRgsn/1Rr5ymBsHqb1w1CG5bJI8wTkCB0DwS8JmnlJOJKeuxFnTqebbhe3FRNr2Bl/6GeLjwhZ8qf9y4YF1zk=%02%02M%0Akh%EF%BF%BD%EF%BF%BD; nickname=%E5%A4%8F%E5%87%89; iconUrl=https://cdn.cnbj1.fds.api.mi-img.com/user-avatar/p015qZyibtfy/IqTUG6xliJpzNQ_320.jpg; signature=; last_login=MI_LOCAL; miui=1; super_user=0; device_info=phone; fe_version=5.0.0; free=0; user_mode=0; most_prefer=1; icon_url=https%3A%2F%2Fcdn.cnbj1.fds.api.mi-img.com%2Fuser-avatar%2Fp015qZyibtfy%2FIqTUG6xliJpzNQ_320.jpg; dark_theme=1; build=744090000; version_name=7.44.9; sys_version=15; sys_version_code=35; isDark=1; user_gender=0; mifes=7d4914a7480c546c042b953ca6ac9d20; token=0v9Y6lI8BhVy4mk7N3HtOhc5fU5-AarCvX8_OKu-nDAIMQKGAKr9REM-B9kJCDVV4JGCkpZiOfMp1zn58yOTJQ..; reg_id=J37kvaHzT3YSFQZ3ZPcEdoeN97Hw0XtYxzNYTSJqR4I=; user_type=1; user_preference={%221%22:1}; p=DKec69eb0843322403c538939a25176463; v=p_o&&ef77c58f82121928|p_di&&D006300c65b1b4bee4ab7d934b18e0cbebbd763|p_dh&&|p_aih&&ec69eb0843322403c538939a25176463|p_dhs&&|p_ss&&d2da63da3cb61c61c543637f34286cf7; device_id=D006300c65b1b4bee4ab7d934b18e0cbebbd763; oaid=ef77c58f82121928; android_id_hash=ec69eb0843322403c538939a25176463; user_id=184956742; client_time=1737141067990"
}

response = requests.post(url, data=payload, headers=headers)
data = response.json()
free_data = data['data']
size = 0
all_size = len(free_data)
wait = []
for i in free_data:
    if i.get('stairs')[0].get('button_status') <= 3:
      size += 1
      wait.append(i)
print(all_size - size)
exit()
import hashlib
url = "https://www.duokan.com/growth/user/task/claim"
key = "CWoAh5nU1pjDGUGpcIxRbfpva1iPK0rb"
#stair_id=1000&task_id=2048&withid=1&uid=184956742&key=CWoAh5nU1pjDGUGpcIxRbfpva1iPK0rb
stair_id=1000
task_id=2048
withid=1
uid=184956742
key="CWoAh5nU1pjDGUGpcIxRbfpva1iPK0rb"
md5 = hashlib.md5(f"stair_id={stair_id}&task_id={task_id}&withid=1&uid={uid}&key={key}".encode()).hexdigest()
print(md5)
payload = {
  'task_id': "2049",
  'stair_id': "1000",
  'sign': md5,
  '_t': "1737136480",
  '_c': "41470"
}

headers = {
  'User-Agent': "Mozilla/5.0 (Linux; Android 15; 23013RK75C Build/AQ3A.240912.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/131.0.6778.201 Mobile Safari/537.36 XiaoMi/MiuiBrowser/2.1.1",
  'Accept': "application/json, text/plain, */*",
  'Accept-Encoding': "gzip, deflate, br, zstd",
  'traceparent': "00-3691a88ce3b6bacec7778c8cbaf16409-af69d43d3f9e03f3-01",
  'sec-ch-ua-platform': "\"Android\"",
  'sec-ch-ua': "\"Android WebView\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"",
  'sec-ch-ua-mobile': "?1",
  'origin': "https://www.duokan.com",
  'x-requested-with': "com.duokan.reader",
  'sec-fetch-site': "same-origin",
  'sec-fetch-mode': "cors",
  'sec-fetch-dest': "empty",
  'referer': "https://www.duokan.com/app/checkIn?from=%E6%88%91%E7%9A%84%E9%A1%B5%E7%AD%BE%E5%88%B0%E5%85%A5%E5%8F%A3",
  'accept-language': "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
  'priority': "u=1, i",
  'Cookie': "app_id=DuoKan; channel=Y6WBAG; api=2; fiction_level=0_1; _m=1; book_level=0_1; random_id=ec69eb0843322403c538939a25176463; first_version=741070000; isYouthMode=false; login_user_id=d2da63da3cb61c61c543637f34286cf7; new_continue_month_strategy=1; phone=23013RK75C; lang=zh_CN; personal_recommend=1; store_pref=publish; personalise_ad=1; fresh_install_time=1720120988468; platform=android; ch=Y6WBAG; hidden_channels=recommend%2Caudio%2Cfemale%2Cmale; max_book_version=2; phoneModel=23013RK75C; osVersion=Android%20; imei=; xiaoMiPhone=true; serviceToken=bl/u3NYyV7kZCdzM921xGJ3hdE5+/pW46B1IX00YL72DDBxbAtmdSfIcW7uaswrYpJP1W/5z8OitHaP0qm14QtHRgsn/1Rr5ymBsHqb1w1CG5bJI8wTkCB0DwS8JmnlJOJKeuxFnTqebbhe3FRNr2Bl/6GeLjwhZ8qf9y4YF1zk=%02%02M%0Akh%EF%BF%BD%EF%BF%BD; nickname=%E5%A4%8F%E5%87%89; iconUrl=https://cdn.cnbj1.fds.api.mi-img.com/user-avatar/p015qZyibtfy/IqTUG6xliJpzNQ_320.jpg; signature=; last_login=MI_LOCAL; miui=1; super_user=0; device_info=phone; fe_version=5.0.0; free=0; user_mode=0; most_prefer=1; icon_url=https%3A%2F%2Fcdn.cnbj1.fds.api.mi-img.com%2Fuser-avatar%2Fp015qZyibtfy%2FIqTUG6xliJpzNQ_320.jpg; dark_theme=1; build=744090000; version_name=7.44.9; sys_version=15; sys_version_code=35; isDark=1; user_gender=0; mifes=7d4914a7480c546c042b953ca6ac9d20; token=0v9Y6lI8BhVy4mk7N3HtOhc5fU5-AarCvX8_OKu-nDAIMQKGAKr9REM-B9kJCDVV4JGCkpZiOfMp1zn58yOTJQ..; reg_id=J37kvaHzT3YSFQZ3ZPcEdoeN97Hw0XtYxzNYTSJqR4I=; user_type=1; user_preference={%221%22:1}; p=DKec69eb0843322403c538939a25176463; v=p_o&&ef77c58f82121928|p_di&&D006300c65b1b4bee4ab7d934b18e0cbebbd763|p_dh&&|p_aih&&ec69eb0843322403c538939a25176463|p_dhs&&|p_ss&&d2da63da3cb61c61c543637f34286cf7; device_id=D006300c65b1b4bee4ab7d934b18e0cbebbd763; oaid=ef77c58f82121928; android_id_hash=ec69eb0843322403c538939a25176463; user_id=184956742; client_time=1737134792246"
}

response = requests.post(url, data=payload, headers=headers)

print(response.text)
# url = "https://cdn.cnbj1.fds.api.mi-img.com/fe-thirty-libraries/react/17/react.production.min.js"

# params = {
#   'v1': ""
# }

# headers = {
#   'User-Agent': "Mozilla/5.0 (Linux; Android 15; 23013RK75C Build/AQ3A.240912.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/131.0.6778.201 Mobile Safari/537.36 XiaoMi/MiuiBrowser/2.1.1",
#   'Accept-Encoding': "gzip, deflate, br, zstd",
#   'origin': "https://www.duokan.com",
#   'sec-ch-ua-platform': "\"Android\"",
#   # 'if-none-match': "W/\"f41e77542bfa8cc798f37484fb0b0a9d\"",
#   'sec-ch-ua': "\"Android WebView\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"",
#   # 'if-modified-since': "Tue, 05 Apr 2022 03:05:24 GMT",
#   'sec-ch-ua-mobile': "?1",
#   'x-requested-with': "com.duokan.reader",
#   'sec-fetch-site': "cross-site",
#   'sec-fetch-mode': "cors",
#   'sec-fetch-dest': "script",
#   'referer': "https://www.duokan.com/",
#   'accept-language': "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
#   'priority': "u=1",
#   "Cache-Control": 'no-cache'
# }

# response = requests.get(url, params=params, headers=headers)

# print(response.text)
