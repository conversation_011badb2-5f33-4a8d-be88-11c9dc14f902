from Cryptodome.Cipher import AES
from Cryptodome.Util.Padding import pad,unpad
import json
import base64
bask = "KV4lJCt3"
bbsk = "X3gyKiZ+Q"
bcsk = "CEjKA=="

basi = "IyZAYV94K3"
bbsi = "IoJSpefi"
bcsi = "EpJA=="

def decode(value):
    # 此函数需要实现与 v.a.decode 相同的功能
    return base64.b64decode(value).decode('utf-8')

def encrypt_data(data):
    key = decode(bask + bbsk + bcsk)
    iv = decode(basi + bbsi + bcsi)

    cipher = AES.new(key.encode('utf-8'), AES.MODE_CBC, iv.encode('utf-8'))
    encrypted = cipher.encrypt(pad(data.encode('utf-8'), AES.block_size))

    return base64.b64encode(encrypted).decode('utf-8')

def decrypt_data(encrypted_data):
    key = decode(bask + bbsk + bcsk)
    iv = decode(basi + bbsi + bcsi)

    cipher = AES.new(key.encode('utf-8'), AES.MODE_CBC, iv.encode('utf-8'))
    encrypted_bytes = base64.b64decode(encrypted_data)

    decrypted = unpad(cipher.decrypt(encrypted_bytes), AES.block_size)

    return decrypted.decode('utf-8')

# 示例使用
# decrypted_data = decrypt_data("你的加密数据")

"FaDuFvJY3837NmqhLnp9+Uqqp3pFxW8/t1rCtoe1Lov90UuulRvWEeKnx+/aQjiFR7K2JiF3uNgUnhQgjK0Eh1hvYiUR+htf+u2gTj+3HnWwQWZU7ujIg9wDCDBXv/sFqvOpCrPlMOgtsruvd7aVkYT3T6+j4kWOaxuZcsxGo3M6n2mGY8Rd1NpNyNM4zEkd4ohIzsnZY4LPTqDyTW2aDLTlmX9cQbH9XExvDG+wdxFVf4VHNM/89PbEFI4PmJ/cqi8uV4EZ4kG/GnChzKa4q2oHT7S8k+yWIaw2oq3ro9O8LWHcdtbfOzR6hSN8RCkRtXd+IUh+Q6k6ka5qAJItiwZofzJfJURcGqcxd5fQYbSPZjb+o9PteWfk5V1A7oFP6JZoj46RyZd2WY5iEiw0mmPMeYqz8aXGmp08cFlROqgdzv5eI4l9ULvX3rGJ9kgHExEPfheE3f0PaPTrkYEf+Oogoxul8PM4WTuysRFA5eJSjqHSoAqju8X8CPesOSVJNM4qlUhSMJY3M+YMnpGjuOFSJNM6Tumk5ukK6cW9OVN7fGEyWhedpOhn/K3tTy/pC2Z11UvmaphgxWOWUoE/AtZ22p4PGZu02473t3jyHDh56gr2p6jMe9lMSHe4Vu/hNeMYvnFqjkgYX8EaoIvscNn63Ev0Dt+xs789NFz/WesPNlazbsokBcQNI8I6T1q0UMCR4y9BMOchVg95klpI2a16DBfc/kDbOGh/CNTTErShny558OsgxbOqg3GDqblIzBKb/3sg8Dlde/0ZqmD5WkGGD4P4euQlB4jli5ulTK58D/7wYqeU+IiG8KkegVfoA63294I3pZvp+j8DJIXzuzLeQMaMdbc2eqVq3TlXlI2HzHgZAZKBnOmHU/XHGzBU6NksFkuMqqhw4lNwZPHqzSFIa5Q4d8rKuOPswAvlFv+nE12rq09l6R2YSYnO5zpR+EVvs1a3ILXj/M6l6Gvjo0CD/DqQhgaNVaQLLpxyvyugfYiv1UFIfbzbhKsDGoP/h8QZvLqOadxilKPxcffWUw6IKOTehAMUlaaAEOfqXW32qhFTWZBpXHDQMgVjZJzUWVWjm/SrdSfAzrz+aTeuYmmBdc6g8ma+mOppxGyt4GOrC7O13+TBPcnZLIUeDFkKsHA6yuet4om2LUkpv8h+CyxehzdQbeupdwdy4h+FrEjUnIL+lA2CIyC5p3uJa2rI+A7sHZIU7T/qaPeoey4wU1MUlR9HwtoSvif+3gBF4+uoxggW8WGwe+PsgiegZ1uE9uPLbU9vs1R2X36iUPMVGNR/YtvNUGWEFHUVd6nAUCK3UcQ4r2Omr5Jjoq+r6xmQy/epIoSyDk8OpO6CsXehcQQlyV1vR1/Y7lNpGaILdLyl/ZLbbcoiX5CHqGaG0ezKxuxM5oXZ2SS7I0+5XqUyxzZtnPuAilvSY3El5w6j225gVMdgC1tJ1zTO3HA6lSBrqsbKqJEAOmQVXGv6tC4pWA=="
#print(encrypt_data('{"common":{"Accept":"application/json, text/plain, */*"},"delete":{},"get":{},"head":{},"post":{"Content-Type":"application/x-www-form-urlencoded"},"put":{"Content-Type":"application/x-www-form-urlencoded"},"patch":{"Content-Type":"application/x-www-form-urlencoded"},"platform":"3","source":"1","deviceSource":"1","userId":"043f93128186d2779bcad43c353b97b769eb66eec28bf80f1e3e5c84f9e21bf69c26c7c4b0a7397c136be8b9007aeeb6c1be862620760b8f2a44d43dc2255fa2db075f388b4e8b82b6330481c12dc9c2bab5f7f4228d146df867d233644dc60168c57b0a2739782e64c4399de4","userToken":"04eabe3d873e96fef0053a5bb2c204bb81c88fdbf49bb1b4c938d4cc48b7e682c6dd51942e94b9d2d89d2071f02cc34d0b6012c75cafd350759abb1fd99c67380729ddd3efb3baa06612547eed4926b697685a0913c15b25661e1aadd377ec98433c8d8e279a701495f871dcb5bcb0d9c7429ebced7f531a55dfe0d0ce674cb9cf","deviceId":"deviceId-1727609560042-833049455","appChannel":"40257749238","ipProvince":"%E6%B1%9F%E8%A5%BF","headerSign":"fed9a5d1f4ba926104676d369f8e575399c85a5155e8a21003ddefb4692341b3","defaultAppChannel":"40257749238","os":3,"appType":"9","timestamp":1727610441,"Content-Type":"application/json"}'))
# print(encrypt_data('null'))
#print(decrypt_data('FaDuFvJY3837NmqhLnp9+Uqqp3pFxW8/t1rCtoe1Lov90UuulRvWEeKnx+/aQjiFR7K2JiF3uNgUnhQgjK0Eh1hvYiUR+htf+u2gTj+3HnWwQWZU7ujIg9wDCDBXv/sFqvOpCrPlMOgtsruvd7aVkYT3T6+j4kWOaxuZcsxGo3M6n2mGY8Rd1NpNyNM4zEkd4ohIzsnZY4LPTqDyTW2aDLTlmX9cQbH9XExvDG+wdxFVf4VHNM/89PbEFI4PmJ/cqi8uV4EZ4kG/GnChzKa4q2oHT7S8k+yWIaw2oq3ro9O8LWHcdtbfOzR6hSN8RCkRtXd+IUh+Q6k6ka5qAJItiwZofzJfJURcGqcxd5fQYbSPZjb+o9PteWfk5V1A7oFP6JZoj46RyZd2WY5iEiw0mmPMeYqz8aXGmp08cFlROqhBEwYqeSHWDK5FU0whIajvgPuKpMT4ZU/4coRHxen9wOnT5Rp9aeUbZmhpT/BsAEcwzN4fwAtRV+AVMz6bDmM2b78n/qhtQlk/Vlxd7TEwxPmeyOTICqf4kAjAmi1+gsHM4ZLIu4WYHUwGTvKl1wWOD/Xfg7SQ7AeCi4mpGfWKp/EXtzSVu3AbhbgyqaWJaCntwIn/VRzg7iazvO/+5RPB/uAEtjnVb1iZaVLlfuyHCZCPPrSvOS2b94dHPD0IuYpschTSZZOL0V4v2otmaZ9GelzacvGLSm5VCKFHSuTvURYw4/U1/jKDkq7mXLxa4t+QYoZx6SplMJSMZ+xN7yDW/61SDHLKO+KIeyNGMp4zdTSUvmEYuev72yjROB/WjpfVBBLibCcv51mGWgpJRTSuCxAc4Q1Q7ir1NTwpPtnv7UDfs87c8QLvskRiNRDOE7PFEGIhxB5tigaq4WITlLSHGmwnyaVg4jJrgbISa8/PPd5d04A+6WO24X5424AgMQKPL+uzJF4SMCHImAumVYY/f9ziT4yvYsqN2QfqB+GCSTz64x2Ct2idxTXwSCZS1mNRFZOgxzOXfeF/RLwYZxnWaIxZ/oqNitdNBBz91g72sFggYYJvRM3JoNRWgS/570gT6lzM4fqUaTyQsIe3ziy1FYnES2kuXtNh9ioRGmTp/+taNNJdGSvq3n2NJ3P53R9tEVLrrJXAo3H2Gact3YvdF6HIi4L0gZwfXYqhzglKTdpF0MbZrjWUJw8PFpUG4HAL3sDQ6vFqWi2+2UnVYNpg7hj0T6bJMUG9dqC8MZxSmUFjrmJdxlGwThqVJRcOjBS42MF7OdODzX+XcoA5Sd/9yU3O83h4JxTFS8hmcYpndlF1ZRdty8V4exqzCirCZXtqg21rEe2IMmCa6XSVfP2lNIXS76v7XDfbIlOD7G4Wf0ZooZktRBlqKYWgSRHwb+5/++NPOMZPktdb+agr+Zkf/PE2GZ2z++m2ZthhtadmvmVfVi3XEB/0za8078jxR+3MqPQ2bTq98ZHO5mBk+dWqrFk/sHooRQsa4JB1QlLiDQ=='))
{
    "transformRequest": {},
    "transformResponse": {},
    "timeout": 10000,
    "xsrfCookieName": "XSRF-TOKEN",
    "xsrfHeaderName": "X-XSRF-TOKEN",
    "maxContentLength": -1,
    "headers": {
        "common": {
            "Accept": "application/json, text/plain, */*"
        },
        "delete": {},
        "get": {},
        "head": {},
        "post": {
            "Content-Type": "application/x-www-form-urlencoded"
        },
        "put": {
            "Content-Type": "application/x-www-form-urlencoded"
        },
        "patch": {
            "Content-Type": "application/x-www-form-urlencoded"
        },
        "platform": "3",
        "source": "1",
        "deviceSource": "1",
        "userId": "043f93128186d2779bcad43c353b97b769eb66eec28bf80f1e3e5c84f9e21bf69c26c7c4b0a7397c136be8b9007aeeb6c1be862620760b8f2a44d43dc2255fa2db075f388b4e8b82b6330481c12dc9c2bab5f7f4228d146df867d233644dc60168c57b0a2739782e64c4399de4",
        "userToken": "04eabe3d873e96fef0053a5bb2c204bb81c88fdbf49bb1b4c938d4cc48b7e682c6dd51942e94b9d2d89d2071f02cc34d0b6012c75cafd350759abb1fd99c67380729ddd3efb3baa06612547eed4926b697685a0913c15b25661e1aadd377ec98433c8d8e279a701495f871dcb5bcb0d9c7429ebced7f531a55dfe0d0ce674cb9cf",
        "deviceId": "deviceId-1727609560042-833049455",
        "appChannel": "40257749238",
        "ipProvince": "%E6%B1%9F%E8%A5%BF",
        "headerSign": "fed9a5d1f4ba926104676d369f8e575399c85a5155e8a21003ddefb4692341b3",
        "appVersion": "*******",
        "defaultAppChannel": "40257749238",
        "os": 3,
        "appType": "9",
        "timestamp": 1727613240,
        "Content-Type": "application/json",
        "mgheaders": "FaDuFvJY3837NmqhLnp9+Uqqp3pFxW8/t1rCtoe1Lov90UuulRvWEeKnx+/aQjiFR7K2JiF3uNgUnhQgjK0Eh1hvYiUR+htf+u2gTj+3HnWwQWZU7ujIg9wDCDBXv/sFqvOpCrPlMOgtsruvd7aVkYT3T6+j4kWOaxuZcsxGo3M6n2mGY8Rd1NpNyNM4zEkd4ohIzsnZY4LPTqDyTW2aDLTlmX9cQbH9XExvDG+wdxFVf4VHNM/89PbEFI4PmJ/cqi8uV4EZ4kG/GnChzKa4q2oHT7S8k+yWIaw2oq3ro9O8LWHcdtbfOzR6hSN8RCkRtXd+IUh+Q6k6ka5qAJItiwZofzJfJURcGqcxd5fQYbSPZjb+o9PteWfk5V1A7oFP6JZoj46RyZd2WY5iEiw0mmPMeYqz8aXGmp08cFlROqgdzv5eI4l9ULvX3rGJ9kgHExEPfheE3f0PaPTrkYEf+Oogoxul8PM4WTuysRFA5eJSjqHSoAqju8X8CPesOSVJNM4qlUhSMJY3M+YMnpGjuOFSJNM6Tumk5ukK6cW9OVN7fGEyWhedpOhn/K3tTy/pC2Z11UvmaphgxWOWUoE/AtZ22p4PGZu02473t3jyHDh56gr2p6jMe9lMSHe4Vu/hNeMYvnFqjkgYX8EaoIvscNn63Ev0Dt+xs789NFz/WesPNlazbsokBcQNI8I6T1q0UMCR4y9BMOchVg95klpI2a16DBfc/kDbOGh/CNTTErShny558OsgxbOqg3GDqblIzBKb/3sg8Dlde/0ZqmD5WkGGD4P4euQlB4jli5ulTK58D/7wYqeU+IiG8KkegVfoA63294I3pZvp+j8DJIXzuzLeQMaMdbc2eqVq3TlXlI2HzHgZAZKBnOmHU/XHGzBU6NksFkuMqqhw4lNwZPHqzSFIa5Q4d8rKuOPswAvlFv+nE12rq09l6R2YSYnO5zpR+EVvs1a3ILXj/M6l6Gvjo0CD/DqQhgaNVaQLLpxyvyugfYiv1UFIfbzbhKsDGoP/h8QZvLqOadxilKPxcffWUw6IKOTehAMUlaaAEOfqXW32qhFTWZBpXHDQMgVjZJzUWVWjm/SrdSfAzrz+aTeuYmmBdc6g8ma+mOppxGyt4GOrC7O13+TBPcnZLIUeDFkKsHA6yuet4om2LUkpv8h+CyxehzdQbeupdwdy4h+FrEjUnIL+lA2CIyC5p3uJa2rI+A7sHZIU7T/qaPeoey4wU1MUlR9HwtoSvif+3gBF4+uoxggW8WGwe+PsgiegZ1uE9uPLbU9vs1R2X36iUPMVGNR/YtvNUGWEFHUVd6nAUCJFFw/rS2zzN6Hh0Fpb1AoCBTTaD/CQja7Olb5Hf4O1zHE1jcbmvQdstVjs1dHX9dDtQsUW8zOc0cH38XtPCXUw61YKn5LQbJmURgUkPL2UjxK7iJ1nzvpnPQhfGd1//WHcKKyWGsCqr3ydvpm6Qma6TNylO7XHTDoLDVTNm75pOLy/Qs7e792UgEQm/RnLc7C56XanKlR1H8zoPJmoiw6M",
        "peekaboo": 1,
        "hgv": "5rPulhA0y"
    },
    "method": "post",
    "baseURL": "https://betagame.migufun.com/",
    "appChannel": "",
    "userPhone": "",
    "url": "/member/newSign/v1.0.6.5/queryReissueSignPop",
    "data": "HiyYxmCc6h9LEvkzZp40mhDrAKk8tI4icsHn3+8EutGRoEOac2de4n/u7LyMcRI1vyb9DLhaFa2aaeU5zW1sBQ==",
    "metadata": {
        "startTime": "2024-09-29T12:34:00.294Z"
    }
}
{"common":{"Accept":"application/json, text/plain, */*"},"delete":{},"get":{},"head":{},"post":{"Content-Type":"application/x-www-form-urlencoded"},"put":{"Content-Type":"application/x-www-form-urlencoded"},"patch":{"Content-Type":"application/x-www-form-urlencoded"},"platform":"3","source":"1","deviceSource":"1","userId":"0474e1f294028d1012a2a930997fb21de7b94fcda75cd3f2ba49ed1f7f71a264b84a607007d32f357288804695d41e5118701424ce9984ac6957efd4883673cc1ee9af59074f91ba4623e2a90db5734679f4a2d9134b704b9f757319e476002aaa450bca5661272124c1910fd0","userToken":"04cfa965063cf4d1579b3ce5b7017616c5517f0206f6bb00893d963d9309d8a445cd2feaf38d943e59414076d59ee69c3d797ca1abb100b8dfe7453fc3e564cbf8041ad518205b322dba536f7e5fc71a48aa4e234dce1a0424959df4f160891efe643f3dfeb4792bedc6497e2612c2be704a36e7502f734b038f574a2d38ef2513","deviceId":"deviceId-1727609560042-833049455","appChannel":"40257749238","ipProvince":"%E6%B1%9F%E8%A5%BF","headerSign":"fed9a5d1f4ba926104676d369f8e575399c85a5155e8a21003ddefb4692341b3","defaultAppChannel":"40257749238","os":3,"appType":"9","timestamp":1727633684,"Content-Type":"application/json"}
{"common":{"Accept":"application/json, text/plain, */*"},"delete":{},"get":{},"head":{},"post":{"Content-Type":"application/x-www-form-urlencoded"},"put":{"Content-Type":"application/x-www-form-urlencoded"},"patch":{"Content-Type":"application/x-www-form-urlencoded"},"platform":"3","source":"1","deviceSource":"1","userId":"043f93128186d2779bcad43c353b97b769eb66eec28bf80f1e3e5c84f9e21bf69c26c7c4b0a7397c136be8b9007aeeb6c1be862620760b8f2a44d43dc2255fa2db075f388b4e8b82b6330481c12dc9c2bab5f7f4228d146df867d233644dc60168c57b0a2739782e64c4399de4","userToken":"04eabe3d873e96fef0053a5bb2c204bb81c88fdbf49bb1b4c938d4cc48b7e682c6dd51942e94b9d2d89d2071f02cc34d0b6012c75cafd350759abb1fd99c67380729ddd3efb3baa06612547eed4926b697685a0913c15b25661e1aadd377ec98433c8d8e279a701495f871dcb5bcb0d9c7429ebced7f531a55dfe0d0ce674cb9cf","deviceId":"deviceId-1727609560042-833049455","appChannel":"40257749238","ipProvince":"%E6%B1%9F%E8%A5%BF","headerSign":"fed9a5d1f4ba926104676d369f8e575399c85a5155e8a21003ddefb4692341b3","appVersion":"*******","defaultAppChannel":"40257749238","os":3,"appType":"9","timestamp":1727613240,"Content-Type":"application/json"}

{
    "common": {
        "Accept": "application/json, text/plain, */*"
    },
    "delete": {

    },
    "get": {

    },
    "head": {

    },
    "post": {
        "Content-Type": "application/x-www-form-urlencoded"
    },
    "put": {
        "Content-Type": "application/x-www-form-urlencoded"
    },
    "patch": {
        "Content-Type": "application/x-www-form-urlencoded"
    },
    "platform": "3",
    "source": "1",
    "deviceSource": "1",
    "userId": "0474e1f294028d1012a2a930997fb21de7b94fcda75cd3f2ba49ed1f7f71a264b84a607007d32f357288804695d41e5118701424ce9984ac6957efd4883673cc1ee9af59074f91ba4623e2a90db5734679f4a2d9134b704b9f757319e476002aaa450bca5661272124c1910fd0",
    "userToken": "04cfa965063cf4d1579b3ce5b7017616c5517f0206f6bb00893d963d9309d8a445cd2feaf38d943e59414076d59ee69c3d797ca1abb100b8dfe7453fc3e564cbf8041ad518205b322dba536f7e5fc71a48aa4e234dce1a0424959df4f160891efe643f3dfeb4792bedc6497e2612c2be704a36e7502f734b038f574a2d38ef2513",
    "deviceId": "deviceId-1727609560042-833049455",
    "appChannel": "40257749238",
    "ipProvince": "%E6%B1%9F%E8%A5%BF",
    "headerSign": "fed9a5d1f4ba926104676d369f8e575399c85a5155e8a21003ddefb4692341b3",
    "defaultAppChannel": "40257749238",
    "os": 3,
    "appType": "9",
    "timestamp": 1727633684,
    "Content-Type": "application/json"
}
decrypted_data = decrypt_data("FaDuFvJY3837NmqhLnp9+Uqqp3pFxW8/t1rCtoe1Lov90UuulRvWEeKnx+/aQjiFR7K2JiF3uNgUnhQgjK0Eh1hvYiUR+htf+u2gTj+3HnWwQWZU7ujIg9wDCDBXv/sFqvOpCrPlMOgtsruvd7aVkYT3T6+j4kWOaxuZcsxGo3M6n2mGY8Rd1NpNyNM4zEkd4ohIzsnZY4LPTqDyTW2aDLTlmX9cQbH9XExvDG+wdxFVf4VHNM/89PbEFI4PmJ/cqi8uV4EZ4kG/GnChzKa4q2oHT7S8k+yWIaw2oq3ro9O8LWHcdtbfOzR6hSN8RCkRtXd+IUh+Q6k6ka5qAJItiwZofzJfJURcGqcxd5fQYbSPZjb+o9PteWfk5V1A7oFP6JZoj46RyZd2WY5iEiw0mmPMeYqz8aXGmp08cFlROqhBEwYqeSHWDK5FU0whIajvgPuKpMT4ZU/4coRHxen9wOnT5Rp9aeUbZmhpT/BsAEcwzN4fwAtRV+AVMz6bDmM2b78n/qhtQlk/Vlxd7TEwxPmeyOTICqf4kAjAmi1+gsHM4ZLIu4WYHUwGTvKl1wWOD/Xfg7SQ7AeCi4mpGfWKp/EXtzSVu3AbhbgyqaWJaCntwIn/VRzg7iazvO/+5RPB/uAEtjnVb1iZaVLlfuyHCZCPPrSvOS2b94dHPD0IuYpschTSZZOL0V4v2otmaZ9GelzacvGLSm5VCKFHSuTvURYw4/U1/jKDkq7mXLxa4t+QYoZx6SplMJSMZ+xN7yDW/61SDHLKO+KIeyNGMp4zdTSUvmEYuev72yjROB/WjpfVBBLibCcv51mGWgpJRTSuCxAc4Q1Q7ir1NTwpPtnv7UDfs87c8QLvskRiNRDOE7PFEGIhxB5tigaq4WITlLSHGmwnyaVg4jJrgbISa8/PPd5d04A+6WO24X5424AgMQKPL+uzJF4SMCHImAumVYY/f9ziT4yvYsqN2QfqB+GCSTz64x2Ct2idxTXwSCZS1mNRFZOgxzOXfeF/RLwYZxnWaIxZ/oqNitdNBBz91g72sFggYYJvRM3JoNRWgS/570gT6lzM4fqUaTyQsIe3ziy1FYnES2kuXtNh9ioRGmTp/+taNNJdGSvq3n2NJ3P53R9tEVLrrJXAo3H2Gact3YvdF6HIi4L0gZwfXYqhzglKTdpF0MbZrjWUJw8PFpUG4HAL3sDQ6vFqWi2+2UnVYNpg7hj0T6bJMUG9dqC8MZxSmUFjrmJdxlGwThqVJRcOjBS42MF7OdODzX+XcoA5Sd/9yU3O83h4JxTFS8hmcYpndlF1ZRdty8V4exqzCirCZXtqg21rEe2IMmCa6XSVfP2lNIXS76v7XDfbIlOD7G4Wf0ZooZktRBlqKYWgSRHwb+5/++NPOMZPktdb+agr+Zkf/PE2GZ2z++m2ZthhtadmvmVfVi3XEB/0za8078jxR+3MqPQ2bTq98ZHO5mBk+dWqrFk/sHooRQsa4JB1QlLiDQ==")
r = json.dumps({"common":{"Accept":"application/json, text/plain, */*"},"delete":{},"get":{},"head":{},"post":{"Content-Type":"application/x-www-form-urlencoded"},"put":{"Content-Type":"application/x-www-form-urlencoded"},"patch":{"Content-Type":"application/x-www-form-urlencoded"},"platform":"3","source":"1","deviceSource":"1","userId":"0474e1f294028d1012a2a930997fb21de7b94fcda75cd3f2ba49ed1f7f71a264b84a607007d32f357288804695d41e5118701424ce9984ac6957efd4883673cc1ee9af59074f91ba4623e2a90db5734679f4a2d9134b704b9f757319e476002aaa450bca5661272124c1910fd0","userToken":"04cfa965063cf4d1579b3ce5b7017616c5517f0206f6bb00893d963d9309d8a445cd2feaf38d943e59414076d59ee69c3d797ca1abb100b8dfe7453fc3e564cbf8041ad518205b322dba536f7e5fc71a48aa4e234dce1a0424959df4f160891efe643f3dfeb4792bedc6497e2612c2be704a36e7502f734b038f574a2d38ef2513","deviceId":"deviceId-1727609560042-833049455","appChannel":"40257749238","ipProvince":"%E6%B1%9F%E8%A5%BF","headerSign":"fed9a5d1f4ba926104676d369f8e575399c85a5155e8a21003ddefb4692341b3","defaultAppChannel":"40257749238","os":3,"appType":"9","timestamp":1727633684,"Content-Type":"application/json"},separators=(",",":"))
encrypted_data = encrypt_data('{"common":{"Accept":"application/json, text/plain, */*"},"delete":{},"get":{},"head":{},"post":{"Content-Type":"application/x-www-form-urlencoded"},"put":{"Content-Type":"application/x-www-form-urlencoded"},"patch":{"Content-Type":"application/x-www-form-urlencoded"},"platform":"3","source":"1","deviceSource":"1","userId":"0474e1f294028d1012a2a930997fb21de7b94fcda75cd3f2ba49ed1f7f71a264b84a607007d32f357288804695d41e5118701424ce9984ac6957efd4883673cc1ee9af59074f91ba4623e2a90db5734679f4a2d9134b704b9f757319e476002aaa450bca5661272124c1910fd0","userToken":"04cfa965063cf4d1579b3ce5b7017616c5517f0206f6bb00893d963d9309d8a445cd2feaf38d943e59414076d59ee69c3d797ca1abb100b8dfe7453fc3e564cbf8041ad518205b322dba536f7e5fc71a48aa4e234dce1a0424959df4f160891efe643f3dfeb4792bedc6497e2612c2be704a36e7502f734b038f574a2d38ef2513","deviceId":"deviceId-1727609560042-833049455","appChannel":"40257749238","ipProvince":"%E6%B1%9F%E8%A5%BF","headerSign":"fed9a5d1f4ba926104676d369f8e575399c85a5155e8a21003ddefb4692341b3","defaultAppChannel":"40257749238","os":3,"appType":"9","timestamp":1727633684,"Content-Type":"application/json"}')
encrypted_data = encrypt_data(f'{r}')
print(decrypted_data)
print(encrypted_data)

import json,datetime
class CryptoUtils:
    @staticmethod
    def get_aes_key():
        date = datetime.datetime.now()
        year = str(date.year)
        month = str(date.month).zfill(2)
        day = str(date.day).zfill(2)
        key = f"{year}{month}{day}{year}{month}{day}"
        return key

    @staticmethod
    def aes_encrypt(data, aes_key=None):
        if data is None or data == "NULL":
            data = ""
        elif not isinstance(data, str):
            data = json.dumps(data)

        aes_key = aes_key or CryptoUtils.get_aes_key()
        parsed_key = aes_key.encode('latin1')

        cipher = AES.new(parsed_key, AES.MODE_CBC, parsed_key)
        encrypted = cipher.encrypt(pad(data.encode('utf-8'), AES.block_size))

        return base64.b64encode(encrypted).decode('utf-8')

    @staticmethod
    def aes_decrypt(encrypted_data, aes_key=None):
        try:
            aes_key = aes_key or CryptoUtils.get_aes_key()
            parsed_key = aes_key.encode('latin1')

            cipher = AES.new(parsed_key, AES.MODE_CBC, parsed_key)
            encrypted_bytes = base64.b64decode(encrypted_data)
            decrypted = unpad(cipher.decrypt(encrypted_bytes), AES.block_size)

            return decrypted.decode('utf-8')
        except Exception:
            return encrypted_data
