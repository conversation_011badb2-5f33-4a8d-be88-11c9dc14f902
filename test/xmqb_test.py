# # {
# #     "nonce": "6596078633643780096"
# # }


# from typing import Dict, Optional
# from urllib.parse import urlparse
# from collections import OrderedDict
# import hashlib
# import base64
# import requests
# import json

# class InvalidParameterException(Exception):
#     """自定义参数无效异常"""
#     pass
# class EncryptionError(Exception):
#     """加密过程中的异常"""
#     pass
# def build_security_signature(
#     app_id: Optional[str],
#     url: Optional[str],
#     parameters: Optional[Dict[str, str]],
#     security_key: str
# ) -> str:
#     """
#     构建安全签名字符串

#     Args:
#         app_id: 应用标识符
#         url: 请求URL地址
#         parameters: 请求参数映射
#         security_key: 安全密钥

#     Returns:
#         生成的安全签名字符串

#     Raises:
#         InvalidParameterException: 当安全密钥为空时抛出异常
#     """
#     # 验证安全密钥
#     if not security_key:
#         raise InvalidParameterException("security is not nullable")

#     # 初始化签名组件列表
#     sign_components = []

#     # 添加应用ID(如果存在，转换为大写)
#     if app_id:
#         sign_components.append(app_id.upper())

#     # 添加URL的编码路径(如果存在)
#     if url:
#         parsed_url = urlparse(url)
#         sign_components.append(parsed_url.path)

#     # 处理参数(如果存在)
#     if parameters:
#         # 使用OrderedDict确保参数按键名排序
#         sorted_params = OrderedDict(sorted(parameters.items()))
#         # 将参数格式化为"key=value"形式
#         sign_components.extend(f"{key}={value}" for key, value in sorted_params.items())

#     # 添加安全密钥
#     sign_components.append(security_key)

#     # 使用'&'连接所有组件
#     final_signature = "&".join(sign_components)

#     # 返回加密后的签名(这里假设f是一个加密函数)
#     return encode_sha1_base64(final_signature, 2)  # 注意：这里的f函数需要自己实现




# def encode_sha1_base64(
#     content: Optional[str],
#     encoding_flags: int = 0
# ) -> Optional[str]:
#     """
#     将输入字符串进行 SHA1 哈希处理后转为 Base64 编码

#     Args:
#         content: 需要加密的字符串
#         encoding_flags: base64编码选项

#     Returns:
#         base64编码后的字符串，如果输入为None则返回None

#     Raises:
#         EncryptionError: 加密过程发生错误时抛出
#     """
#     if content is None:
#         return None

#     try:
#         # 计算SHA1哈希值
#         sha1_bytes = calculate_sha1(content.encode('utf-8'))
#         if sha1_bytes is None:
#             return None
#         # 转换为base64编码
#         return base64.b64encode(sha1_bytes).decode('utf-8')
#     except Exception as e:
#         raise EncryptionError(f"SHA1加密失败: {str(e)}")

# def calculate_sha1(data: Optional[bytes]) -> Optional[bytes]:
#     """
#     计算输入数据的SHA1哈希值

#     Args:
#         data: 需要计算哈希值的字节数据

#     Returns:
#         SHA1哈希值的字节串，如果输入为None或空则返回None

#     Raises:
#         EncryptionError: 计算SHA1过程发生错误时抛出
#     """
#     if not data:
#         return None

#     try:
#         sha1_hash = hashlib.sha1()
#         sha1_hash.update(data)
#         return sha1_hash.digest()
#     except Exception as e:
#         raise EncryptionError(f"SHA1计算失败: {str(e)}")
# # ssecurity="mCy15+gQIy4YCRj9wJLKVQ=="
# # treeMap = {
# #     "nonce": "5283093463904494592",
# # }
# # # ssecurity="jjxRiTfWl4kez7PxTiOGnA=="
# # # treeMap = {
# # #     "nonce": "6596078633643780096",
# # # }
# # re = build_security_signature(None,None,treeMap,ssecurity )
# # print(re)

# url = "https://account.xiaomi.com/pass/serviceLogin"

# params = {
#   '_json': "true",
#   'appName': "com.mipay.wallet",
#   'sid': "jrairstar",
#   '_locale': "zh_CN"
# }

# headers = {
#   'User-Agent': "23013RK75C/mondrian; HyperOS/OS2.0.3.0.VMNCNXM E/OS2.0 B/S L/zh-CN LO/CN APP/xiaomi.account APPV/********* MK/UmVkbWkgSzYw SDKV/6.2.6.master CPN/com.mipay.wallet",
#   'Connection': "Keep-Alive",
#   'Accept-Encoding': "gzip",
#   'Content-Type': "application/x-www-form-urlencoded",
#   'Cookie': "fidNonce=eyJ0cCI6Im4iLCJub25jZSI6IjVJT1M0aHZsMkg4QnVkUzAiLCJ2IjoiNi4yLjYubWFzdGVyIn0=; passToken=V1:DXmurwq2/R1BHTELu6obCdblrkCQY/L2oOqyUnq8CUAKc+iXb8pQX8qqiaKwBoRRkqbW/jUEd8qGoimQ7iB328ZAirSkTkqz61KGRBqI0UAMBX2F/DuUX6yCjubLmZUK+bsRHTLG+OezSE8Y5eH1vc2oJ5Vpg6GA6eZ6xrFI/eexiR7FvskIcZ7/HQdrJKOu1igwpJnGZcmIfPXCDfTIlywEO1VmzLibx3bLm1c9WiNPOZumhhGDy6yFitgw8dx4/fKs+iQEch+/6pqneMb+NFbngMHpRy1gbjA1lB4P3JFxS68SUpjs52p/g3QwIRlN; rnd=fb1d9cc6; pass_o=ef77c58f82121928; biz_src=systemAccount; userId=*********; deviceId=apAwt8HLgxbrztcP; fidNonceSign=MEUCIBPjwwJZpTTupmAp9lhMjB7kb5TVLYE8ZRsnbnEd7I_IAiEA06LhfOo7JcbgDJpH3YlI8l0mhs2dwImB1BjWvKVgjR0=; uDevId=ud:4baF8LJCUXi5R9z6"
# }

# response = requests.get(url, params=params, headers=headers)
# res_text = response.text
# res_json = json.loads(res_text.removeprefix("&&&START&&&"))

# ssecurity=res_json.get("ssecurity")
# treeMap = {
#     "nonce": f"{res_json.get('nonce')}",
# }
# client_sign = build_security_signature(None,None,treeMap,ssecurity)

# url = res_json.get("location")
# params = {
#     "_userIdNeedEncrypt": "true",
#     "clientSign": client_sign
# }
# new_header = {
#   "Host": "api.jr.airstarfinance.net",
#   "User-Agent": "23013RK75C/mondrian; HyperOS/OS2.0.3.0.VMNCNXM E/OS2.0 B/S L/zh-CN LO/CN APP/xiaomi.account APPV/********* MK/UmVkbWkgSzYw SDKV/6.2.6.master CPN/com.mipay.wallet",
#   "Content-Type": "application/x-www-form-urlencoded",
#   "Cookie": "rnd=fb1d9cc6; biz_src=systemAccount; fidNonce=eyJ0cCI6Im4iLCJub25jZSI6IjRPOGdudGtvc1g4QnVkUzAiLCJ2IjoiNi4yLjYubWFzdGVyIn0=; deviceId=apAwt8HLgxbrztcP; fidNonceSign=MEQCIC_Adw8mz4JYX9MIzfxOdxqK85YtxvX6JkmmqcHqDVTcAiBEHBBRC9xJFRJEvvl4hy4fG5oyxR1HrdWjvqrvP91rIg==",
#   "Connection": "Keep-Alive",
#   "Accept-Encoding": "gzip"
# }
# s = requests.Session()
# response = s.get(url, params=params, headers=headers)
# print(response.text)
# set_cookies = requests.utils.dict_from_cookiejar(s.cookies)
# print(set_cookies)
# url = "https://m.jr.airstarfinance.net/mp/api/passport/getUserProfile"
# response = s.get(url)
# #data = response.json()
# print(response.text)
import uuid
import time
res = str(uuid.uuid4()) + str(int(time.time()))
printf(res)
