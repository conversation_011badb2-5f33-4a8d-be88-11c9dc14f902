# !/usr/bin/python3
# -- coding: utf-8 --
# -------------------------------
# 霸王茶姬
# <AUTHOR>
# @Modify qianfanguojin
# @Time 2024.11.09
# @Description
# ✨ 功能：
#       霸王茶姬小程序, 签到获取积分，积分可换优惠券和实物
# ✨ 抓包步骤：
#       无需抓包，只需登录的账号和密码
#       组装为: 账号;密码
# ✨ 变量示例：
#     export BWCJ_CK='qm-user-token;userId'，多账号换行分割
# -------------------------------
# cron "27 9 * * *" script-path=xxx.py,tag=匹配cron用
# const $ = new Env('霸王茶姬小程序')
# -------------------------------
import traceback
import requests
from script_tools import BaseRun
import os
import time
import hashlib
import json
import execjs
import random
from urllib.parse import quote
class BWCJCrypt:

    def type_1475(N, url):
        """
        构造带签名的 URL，并生成最终编码的字符串。

        参数：
        - N: 需要附加到基础 URL 的字符串

        返回：
        - 经过签名和编码的结果字符串
        """
        # 基础 URL
        base_url = url
        # 拼接输入参数到 URL
        full_url = base_url + N
        # 生成签名数据
        signature_data = f"{BWCJCrypt.exec_js_sig(full_url)}|0|{int(time.time() * 1000)}|1"
        # 调用 ua 函数生成编码结果
        encoded_result = BWCJCrypt.ua(signature_data, True)
        # 使用 URI 编码最终结果
        final_result = quote(encoded_result)
        return final_result
    def exec_js_sig(full_url):
        ctx = execjs.compile("""
            function sig(L) {
                for (var N = 0, g = encodeURIComponent(L), B = 0; B < g['length']; B++)
                    N = (((N << 7) - N) + 398) + g['charCodeAt'](B),N |= 0;
                return N
            };
        """)
        return ctx.call("sig", full_url)

    def ua(input_data, flag):
        """
        模拟 JavaScript 中的 ua 函数逻辑。

        参数：
        - input_data: 输入的数据
        - flag: 标志位，用于决定是否返回中间结果

        返回：
        - 根据逻辑处理后的字符串
        """
        # 初始化变量
        base64_chars = "DGi0YA7BemWnQjCl4+bR3f8SKIF9tUz/xhr2oEOgPpac=61ZqwTudLkM5vHyNXsVJ"  # 自定义 Base64 字符集
        steps = ['3', '4', '2', '1', '0']  # 模拟步骤顺序
        current_step = 0

        # 无限循环（通过步骤控制跳出）
        while True:
            step = steps[current_step]
            current_step += 1

            if step == '4':  # 第一步：检查输入是否为空
                if input_data is None:
                    return ''
                continue

            elif step == '3':  # 第二步：初始化自定义 Base64 字符集
                base64_dict = {"uGGDj": base64_chars}
                continue

            elif step == '2':  # 第三步：调用 `uu` 函数，将输入编码为 Base64 格式
                def map_to_base64(index):
                    """根据索引返回自定义 Base64 字符集中的字符"""
                    return base64_dict["uGGDj"][index]

                # 假设 uu 函数已经实现，以下调用使用 uu 进行编码
                encoded_data = BWCJCrypt.uu(input_data, 6, map_to_base64)  # 使用 6 位进行编码
                continue

            elif step == '1':  # 第四步：检查是否需要直接返回编码结果
                if flag:
                    return encoded_data
                continue

            elif step == '0':  # 第五步：补齐 Base64 编码字符串长度
                padding_needed = len(encoded_data) % 4  # 计算需要的补齐长度
                if padding_needed == 0:
                    return encoded_data
                elif padding_needed == 1:
                    return encoded_data + "==="
                elif padding_needed == 2:
                    return encoded_data + "=="
                elif padding_needed == 3:
                    return encoded_data + "="

            break

    def uu(input_str, bit_width, write_func):
        """
        基于 LZW 算法的字符串压缩实现

        参数：
        - input_str: 需要压缩的输入字符串
        - bit_width: 输出的位宽（缓冲区大小，决定每次写入的位数）
        - write_func: 用于处理写入缓冲区的函数（例如，将数字转换为字符或写入文件）

        返回：
        - 压缩后的字符串结果
        """
        if input_str is None:
            return ""

        # 初始化变量
        dictionary = {}  # 主字典，用于存储编码表
        temp_dict = {}   # 临时字典，用于存储初始化的字符映射
        current_str = "" # 当前正在处理的字符串片段
        result = []      # 最终输出结果
        buffer = 0       # 位缓冲区
        buffer_size = 0  # 当前缓冲区中已使用的位数
        next_code = 3    # 字典中下一个可用的编码值
        max_code = 2     # 当前位宽允许的最大编码值
        current_width = 2  # 当前编码位宽
        input_length = len(input_str)

        # 初始化字典，记录输入字符串中所有的单个字符
        for char in set(input_str):
            dictionary[char] = next_code
            temp_dict[char] = True
            next_code += 1

        def write_bits(value, num_bits):
            """
            将指定的值写入缓冲区，并在缓冲区满时输出
            参数：
            - value: 要写入的值
            - num_bits: 写入的位数
            """
            nonlocal buffer, buffer_size, result
            for _ in range(num_bits):
                buffer = (buffer << 1) | (value & 1)  # 将值的最低位添加到缓冲区
                value >>= 1
                buffer_size += 1
                if buffer_size == bit_width:  # 缓冲区已满，输出并清空
                    result.append(write_func(buffer))
                    buffer = 0
                    buffer_size = 0

        # 遍历输入字符串
        for char in input_str:
            new_str = current_str + char  # 将当前字符拼接到片段中
            if new_str in dictionary:
                current_str = new_str  # 如果新片段已在字典中，则继续累积
            else:
                # 如果新片段不在字典中，编码当前片段并输出
                if current_str in temp_dict:
                    # 当前片段是单个字符，使用 ASCII 或 Unicode 编码
                    char_code = ord(current_str[0])
                    if char_code < 256:
                        write_bits(0, current_width)  # 标记为 8 位字符
                        write_bits(char_code, 8)
                    else:
                        write_bits(1, current_width)  # 标记为 16 位字符
                        write_bits(char_code, 16)
                    temp_dict.pop(current_str)  # 从临时字典中移除该片段
                else:
                    # 当前片段已在主字典中，直接写入编码值
                    write_bits(dictionary[current_str], current_width)

                # 将新片段添加到字典中
                dictionary[new_str] = next_code
                next_code += 1
                # 如果字典编码值超出当前位宽范围，增加位宽
                if next_code > max_code:
                    max_code = 2 ** current_width
                    current_width += 1

                current_str = char  # 重置当前片段为当前字符

        # 处理最后剩余的片段
        if current_str:
            if current_str in temp_dict:
                char_code = ord(current_str[0])
                if char_code < 256:
                    write_bits(0, current_width)
                    write_bits(char_code, 8)
                else:
                    write_bits(1, current_width)
                    write_bits(char_code, 16)
            else:
                write_bits(dictionary[current_str], current_width)

        # 写入结束标记
        write_bits(2, current_width)

        # 清空缓冲区
        if buffer_size > 0:
            buffer <<= (bit_width - buffer_size)  # 将剩余位填充为 0
            result.append(write_func(buffer))

        # 返回压缩结果
        return "".join(result)

    # GET 请求
    def get_request(url):
        N = ''
        result = BWCJCrypt.type_1475(N,url)
        return result
    # POST 请求
    def post_request(json_data, url):
        result = BWCJCrypt.type_1475(json.dumps(json_data,separators=(",", ":")), url)
        return result
class Run(BaseRun):

    def init_vars(self):
        self.session = requests.Session()
        self.session.verify = False
        self.headers = {
            "Host": "miniapp.qmai.cn",
            "Connection": "keep-alive",
            "qm-from": "wechat",
            "charset": "utf-8",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c11)XWEB/11275",
            "content-type": "application/json",
            "Accept-Encoding": "gzip,compress,br,deflate",
            "qm-from-type": "catering",
            "store-id": "49006",
            "accept": "v=1.0",
            "Referer": "https://servicewechat.com/wxafec6f8422cb357b/202/page-frame.html"
        }
        self.appid = 'wxafec6f8422cb357b'
        self.activity_id='947079313798000641'
        self.store_id='49006'
        self.base_url = "https://miniapp.qmai.cn"
    def process_var(self, info):
        ip_address = f"123.123.{random.randint(1, 254)}.{random.randint(1, 254)}"
        self.token, self.user_id = info.split(";")
        self.session.headers.update({'qm-user-token': self.token})
        self.session.headers.update({'X-Forwarded-For': ip_address})
        self.session.headers.update({'X-Real-IP': ip_address})
        self.session.headers.update(self.headers)
        self.timestamp=int(time.time() * 1000)
        self.sign_str=self.getSign()

    def getSign(self):
        # 反转 activity_id
        reversed_activity_id = self.activity_id[::-1]
        # 构建参数对象
        params = {
            'activityId': self.activity_id,
            'sellerId': self.store_id if self.store_id is not None else None,
            'timestamp': self.timestamp,
            'userId': self.user_id
        }
        # 按键排序并构建查询字符串
        sorted_params = sorted(params.items())
        query_string = '&'.join(f'{key}={value}' for key,
                                value in sorted_params if value is not None)
        query_string += f'&key={reversed_activity_id}'
        # 生成 MD5 哈希
        md5_hash = hashlib.md5(query_string.encode()) .hexdigest().upper()
        return md5_hash
    #检查登录信息
    def check_login(self):
        # 请求的参数
        params = {'appid': self.appid}
        # 发送GET请求
        response = self.session.get(f'{self.base_url}/web/catering/crm/personal-info', json=params)
        result = response.json()
        # 检查请求是否成功
        if result.get('code','-1') == '0':
            # 提取个人信息
            mobile_phone = result['data']['mobilePhone'] if 'data' in result and 'mobilePhone' in result[
                'data'] else None
            self.mobile_phone = mobile_phone[:3] + "*" * 4 + mobile_phone[7:]
            self.name = result['data']['name'] if 'data' in result and 'name' in result['data'] else None
            self.logger.info(f"✅ 登陆成功！\n用户名：【{self.name}】 \n手机号：【{self.mobile_phone}】")
            return True
        else:
            # 如果请求不成功，则打印错误信息
            message = result.get('message', '')
            self.logger.error(f'❌ 登录失败: {message}')
        # 最终返回请求是否成功的标志
        return False

    #签到
    def sign(self):
        json_data = {
            'activityId': self.activity_id,
            'appid': self.appid
        }

        #Send the POST request
        url = f'{self.base_url}/web/cmk-center/sign/userSignStatistics'
        response = self.session.post(f'{url}?type__1475=n4+xg7eQwxyAD7DRDBqboNEmY%3DDkDfxGKErQx', json=json.dumps(json_data))
        print(response.text)
        result = response.json()

        # Check if the request was successful
        if result.get('code') == 0:
            data = result.get('data', {})
            sign_days = data.get('signDays', '')
            sign_status = data.get('signStatus', 0) == 1
            self.logger.info(f'✅ 新版签到今天{"已" if sign_status else "未"}签到, 已连续签到{sign_days}天')
            if sign_status:
                return True
        else:
            message = result.get('message', '')
            self.logger.error(f'❌ 查询新版签到失败: {message}')
            return False

        json_data = {
            'activityId': self.activity_id,
            'storeId': self.store_id,
            'timestamp': self.timestamp,
            'signature': self.sign_str,
            'appid': self.appid
        }
        response = self.session.post(f'{self.base_url}/web/cmk-center/sign/takePartInSign', json=json_data)
        result = response.json()
        status_code = response.status_code
        if result.get('code', status_code) == 0:
            data = result.get('data',{})
            rewardDetailList = data.get('rewardDetailList',[{}])
            if rewardDetailList:
                rewardName = rewardDetailList[0].get('rewardName','')
                sendNum = rewardDetailList[0].get('sendNum','')
                self.logger.info(f'✅ 新版签到成功，获得【{sendNum}】{rewardName}')
                return True
        else:
            message = result.get('message', '')
            self.logger.error(f'❌ 新版签到失败: {message}')
            return False
    #积分信息
    def points_info(self):
        json_data = {
            'appid': self.appid
        }
        response = self.session.post(f'{self.base_url}/web/catering/crm/points-info?type__1475={BWCJCrypt.post_request(json_data,)}', json=json_data)
        result = response.json()
        if result.get('code') == '0':
            data = result.get('data', {})
            soon_expired_points = data.get('soonExpiredPoints', 0)
            total_points = data.get('totalPoints', 0)
            expired_time = data.get('expiredTime', '')
            if soon_expired_points:
                self.logger.info(f'ℹ️  有【{soon_expired_points}】积分将于（ {expired_time}）过期')
            self.logger.info(f'✅ 当前积分: 【{total_points}】')
            return total_points, soon_expired_points, expired_time
        else:
            message = result.get('message', '')
            self.logger.error(f'❌ 查询积分失败: {message}')
            return False
    def process(self):
        self.logger.info(f"当前版本：{local_version}")
        if self.app_env_infos:
            self.logger.info(f"\n=======\t共获取到 {len(self.app_env_infos)} 个账号")
        else:
            return
        for index, info in enumerate(self.app_env_infos):
            try:
                self.process_var(info)
                self.logger.info(f"\n=======\t开始执行第 {index + 1} 个账号：{self.user_id}")
                self.logger.info("\n==> 检查登录信息")
                #if self.check_login():
                self.logger.info("\n==> 签到")
                self.sign()
                self.logger.info("\n==> 获取签到结果")
                self.points_info()
                return True
            except Exception:
                self.logger.error(traceback.format_exc())
                return False


if __name__ == "__main__":
    app_name = "霸王茶姬"
    app_env_name = "BWCJ_CK"
    local_script_name = os.path.basename(__file__)
    local_version = '2024.11.9'
    run = Run(app_name=app_name,
              app_env_name=app_env_name)
    run.main()
