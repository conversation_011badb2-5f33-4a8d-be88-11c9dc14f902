auth_method = "phone-captcha"
def process_event(event_data):
    # 获取加密字符串的随机部分，并存入cookie
    encrypted_split = cookie_set(random_split(event_data['encrypt']))

    # 从cookie中获取存储的随机加密字符串
    cookie_value = cookie_get(encrypted_split)

    # 通过cookie值获取偏移量
    offset_value = get_offset2(cookie_value)

    print(offset_value)
    return
    # 更新偏移量到系统中
    update_system("setOFFSET", offset_value)

    # 设置用户ID
    update_system("setUid", event_data['user_id'])

    # 设置用户信息，包括是否登录、用户ID和调试状态
    update_system("setUser", {
        'logined': bool(event_data['user_id']),
        'user_id': event_data['user_id'],
        'debug': event_data['debug']
    })

    # 设置认证token
    update_system("setToken", event_data['token'])

    # 根据认证方法和密码情况进行cookie的设置
    if auth_method in ['phone-captcha', 'auth-token'] or auth_method_has_password(auth_method):
        set_cookie("NCG-token", event_data['token'])
        set_cookie("NCG-eid", encrypted_split)
        set_cookie("NCG-id", event_data['user_id'])
    else:
        set_cookie("NCG-eid-other", encrypted_split)
        set_cookie("NCG-token-other", event_data['token'])

    # 如果需要，加密信息也存入cookie
    if event_data.get('encrypt'):
        set_cookie("NCG-encrypt", event_data['encrypt'])

    # 返回事件数据
    return event_data

# 以下为相关辅助函数的假设实现（仅示例）
def random_split(encrypt_data):
    """
    将输入字符串按照逗号进行分割，提取每个分割项中的第二部分（以冒号分割），
    然后将提取的部分用 'd' 连接成一个新的字符串。

    参数:
    encrypt_data (str): 输入的加密字符串，格式为 "key1:value1,key2:value2,..."

    返回:
    str: 提取后的字符串，各部分用 'd' 连接。
    """
    # 先按逗号分割字符串，再对每个部分按冒号分割，取第二部分，用'd'连接
    return 'd'.join([part.split(":")[1] for part in encrypt_data.split(",")])

import random
import string

def cookie_set(data):
    """
    将输入字符串的每个字符转换为其对应的ASCII值，并与随机生成的字符串组合，
    然后用 '-' 连接成一个新的字符串。

    参数:
    data (str): 输入的字符串。

    返回:
    str: 处理后的字符串，字符的ASCII值与随机字符串组合。
    """
    # 将输入字符串拆分成字符列表
    char_list = list(data)

    # 对每个字符进行处理，将其ASCII值和随机字符串组合
    processed_chars = [
        f"{ord(char)}-{random_string()}" for char in char_list
    ]

    # 将处理后的字符列表用'-'连接成一个字符串
    return "-".join(processed_chars)

def random_string():
    """
    生成一个包含两位小写字母和数字的随机字符串。

    返回:
    str: 随机生成的两位字符串。
    """
    return ''.join(random.choices(string.ascii_lowercase + string.digits, k=2))

def cookie_get(data):
    """
    从输入的经过编码的字符串中提取出每个字符的 ASCII 值，并将这些 ASCII 值转换为字符，
    最终组合成原始的字符串。

    参数:
    data (str): 输入的经过编码的字符串，格式为 "ASCII-随机串-ASCII-随机串-...".

    返回:
    str: 解码后的原始字符串。
    """
    # 如果输入数据存在，则按 '-' 分割，否则返回空列表
    split_data = data.split("-") if data else []

    # 过滤出原始的ASCII值部分（偶数位置的元素），并将其转换为字符
    ascii_values = [int(split_data[i]) for i in range(0, len(split_data), 2)]

    # 将ASCII值列表转换为字符串
    return ''.join([chr(value) for value in ascii_values])

def get_offset2(data):
    """
    处理输入字符串，将其按 'd' 分割，转换为整数后，调用 get_offset 方法
    来计算偏移量。

    参数:
    data (str): 输入的字符串，格式为 "num1dnum2dnum3...".

    返回:
    int: 计算得到的偏移量。
    """
    # 将输入字符串按 'd' 分割，转换为整数
    numbers = [int(part) for part in data.split("d")]

    # 调用 get_offset 函数，传入分割后的整数列表（去掉第一个元素）
    return get_offset(*numbers[1:])

def get_offset(e, t, i):
    """
    计算偏移量，根据公式 (e | t) % 256 + i。

    参数:
    e (int): 第一个整数。
    t (int): 第二个整数。
    i (int): 第三个整数。

    返回:
    int: 计算得到的偏移量。
    """
    return (e | t) % 256 + i
def auth_method_has_password(auth_method):
    # 模拟判断认证方法是否需要密码
    return auth_method == 'password'
event_data = {
    "avatar": "null",
    "channel": "netease",
    "create_time": 1673680649,
    "created_by": "phone-captcha",
    "debug": "false",
    "encrypt": "method:1,number:785091075,timestamp:1729581320,salt:83",
    "free_time_left": 0,
    "games_playing": [],
    "is_new": "false",
    "nickname": "\u7528\u6237aaaa7Xuji",
    "pc_free_time_left": 3000,
    "phone": "18279492761",
    "register_time": 1673680649,
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3Mjk1ODEzMjAsIm5iZiI6MTcyOTU4MTMyMCwianRpIjoiMGJkMGVhMDMtNzYzNy00ZmJiLWE4MmQtMWNlZjU0ZmFlNThlIiwiaWRlbnRpdHkiOiI2M2MyNTcwOTlkNGY1MTljZjlmMDlhYWUiLCJmcmVzaCI6ZmFsc2UsInR5cGUiOiJhY2Nlc3MiLCJ1c2VyX2NsYWltcyI6eyJudW1iZXIiOjc4NTA5MTA3NSwidGltZXN0YW1wIjoxNzI5NTgxMzIwLCJzYWx0Ijo4M319.hcEi9Z0QXcLakIMPUZNO6l_KUpsW9CWngiFOCayenjg",
    "user_id": "63c257099d4f519cf9f09aae"
}

    # "t": "49-pq-100-53-55-sh-56-5b-53-zm-48-r1-57-oz-49-25-48-8f-55-w7-53-6x-100-gv-49-ly-55-zk-50-2k-57-tq-53-vc-56-kn-49-tl-51-lc-50-nv-48-zi-100-v3-56-ed-51-7f",
    # "s": "1d785091075d1729581320d83",
    # "o": 94
process_event(event_data1)
