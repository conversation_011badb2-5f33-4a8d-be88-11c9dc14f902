#1. 米金
import requests
import json

url = "https://m.mi.com/mtop/navi/venue/batch?page_id=13880&pdl=mishop&sign=1a04e866dd9d421e5e16e21bb984b947"

payload = {
  "query_list": [
    {
      "resolver": "infinite-task",
      "sign": "ff8960139490adb9071ed47a34f179ff",
      "parameter": "{\"actId\":\"6706c0695404a23dfb5b2cab\",\"taskTypeList\":[101,200,110,201,202]}",
      "variable": {}
    }
  ]
}

headers = {
  'User-Agent': "Mozilla/5.0 (Linux; U; Android 15; zh-CN; 23013RK75C Build/AQ3A.240912.001; AppBundle/com.mipay.wallet; AppVersionName/6.91.1.5302.2333; AppVersionCode/20577603; MiuiVersion/stable-OS2.0.9.0.VMNCNXM; DeviceId/mondrian; NetworkType/WIFI; mix_version; WebViewVersion/136.0.7103.61) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Mobile Safari/537.36 XiaoMi/MiuiBrowser/4.3",
  'Accept-Encoding': "gzip, deflate, br, zstd",
  'Content-Type': "application/json",
  'sec-ch-ua-platform': "\"Android\"",
  'x-user-agent': "channel/mishop platform/mishop.m",
  'equipmenttype': "4",
  'sec-ch-ua': "\"Chromium\";v=\"136\", \"Android WebView\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
  'sec-ch-ua-mobile': "?1",
  'origin': "https://m.mi.com",
  'x-requested-with': "com.mipay.wallet",
  'sec-fetch-site': "same-origin",
  'sec-fetch-mode': "cors",
  'sec-fetch-dest': "empty",
  'referer': "https://m.mi.com/mfbs/dghd/m_currecy?_rt=rn&pageid=13880&pdl=mishop&sign=1a04e866dd9d421e5e16e21bb984b947&mute=*******&muteType=1&g_utm=MIUI.App.Icon.MiPay.0507MJSC-CT",
  'accept-language': "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
  'priority': "u=1, i",
  #'Cookie': ""
}

response = requests.post(url, data=json.dumps(payload), headers=headers)
res_json = response.json()
tasks = res_json['data']['result_list'][0]['components']


def do_task(task):
  import requests
  import json

  print(task['taskName'])
  if task['status'] != 2:
    return
  mtop_cookie = "serviceToken=320SqGVQ%2BEOtWbELh9x%2FZ%2FL3gPJxUdzJmhNvtKE60efG1Uq6kWwKJe1AWdWDMlUYxU8zkbQV4jbFZau8XaerDw3W%2Ff9jj5ohiykiPIIq%2FfmNWliqMk%2BDaIgzV65Fd5Xsnqp2JvTsAYI8z3kCwwYeg7FfeH4gtTCTW7J3owSvbW5ZC6n36NCnHeN9ovICYylE09iIXoLnNJm8wSJ%2BbKcpExiFzN0ATEhNrgL8eHeBP%2B4%3D;"
 
  mtop_header = {
    'User-Agent': "Mozilla/5.0 (Linux; U; Android 15; zh-CN; 23013RK75C Build/AQ3A.240912.001; AppBundle/com.mipay.wallet; AppVersionName/6.91.1.5302.2333; AppVersionCode/20577603; MiuiVersion/stable-OS2.0.9.0.VMNCNXM; DeviceId/mondrian; NetworkType/WIFI; mix_version; WebViewVersion/136.0.7103.61) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Mobile Safari/537.36 XiaoMi/MiuiBrowser/4.3",
    'Accept-Encoding': "gzip, deflate, br, zstd",
    'Content-Type': "application/json",
    'sec-ch-ua-platform': "\"Android\"",
    'x-user-agent': "channel/mishop platform/mishop.m",
    'equipmenttype': "4",
    'sec-ch-ua': "\"Chromium\";v=\"136\", \"Android WebView\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
    'sec-ch-ua-mobile': "?1",
    'origin': "https://m.mi.com",
    'x-requested-with': "com.mipay.wallet",
    'sec-fetch-site': "same-origin",
    'sec-fetch-mode': "cors",
    'sec-fetch-dest': "empty",
    'accept-language': "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
    'priority': "u=1, i",
    'Cookie': mtop_cookie
  }

  # 完成任务
  do_url = "https://m.mi.com/mtop/mf/act/infinite/do"
  payload = [
    {},
    {
      "taskId": task['taskId'],
      "actId": task['actId']
    }
  ]
  do_res = requests.post(do_url, data=json.dumps(payload), headers=mtop_header)
  res_json = do_res.json()
  taskToken = res_json['data']['taskToken']

  # 领取奖励
  done_url = "https://m.mi.com/v2/mtop/act/lego/task/done/v2"
  if task['taskType'] == 110:
    done_url = "https://m.mi.com/mtop/mf/act/infinite/done"
    pass
  elif task['taskType'] != 200:
    return

  payload = [
    {},
    {
      "taskToken": taskToken,
      "actId": task['actId'],
      "taskType": task['taskType']
    }
  ]
  done_res = requests.post(done_url, data=json.dumps(payload), headers=mtop_header)
  done_res_json = done_res.json()
  print(done_res_json)
  pass


for task in tasks:
  do_task(task)
