import urllib3
from urllib3.exceptions import InsecureRequestWarning
import requests
from Cryptodome.Cipher import AES
from Cryptodome.Util.Padding import pad,unpad
import base64
import json

# 禁用安全请求警告
urllib3.disable_warnings(InsecureRequestWarning)
url = "http://betagame.migufun.com/member/newSign/v1.0.7.7/reportLookAds"

payload = "tE0gx3d9Iud/Svs4RmXivQ=="

headers = {
  'User-Agent': "okhttp/3.9.1",
  'Connection': "Keep-Alive",
  'Accept-Encoding': "gzip",
  'mgHeaders': "7JZxSVYdvPmfoWNpzQiBPWQzeocIXqK69jgulvuZ0s2GKYKfV2jXml28VyBItZMvuYRlz6GqdMWj36hYzOPrKgvaG0WS7Wi+DVMD75rLtGgW4dASEoigvTMjcEOn85KXBZTwaBKDGh6rDJn0YTkQtnZCLaZZdZtKbPhyst1s8vtv6iq+1G+j4oV6i1UHBARChzDXTzaqqSwKvjBQ/I+d1qOeiLDIS/q8LLov1Dgk3mhsBfzvNwAOffBOCNj9OiFlzoGoPhwDEFeG+6czIXCmK9fk5b7E9QHrqEFmScBEgAOHDv+OI0NHJjvWxM5zlHQokvnwzcRPmGXr65cIjONUtjFzYrkGGt7nmeD2tTDkPEZGmXTauVGwou+D3bRpRqk2SBdH9srL9vvsczqBzcsqB8ibMz5udGmbTTmKAxwXfNGokAo73gyDRyVfSOWe35bvz2/J3+gJv/kNttx5138chBNyh0EvrQgLLD7/Qm4sBL85WsO+sWYhHG/Xid65UUqUjtV5InrwnP8WShtC/t5JgjJ64z5NlLyW6NYpMsbPGiRfCJdXaD510Iyg3QY2OoIZWOfnDBJjt5TlPKkHxl/C7YtHfkIPKAmumX40LpYv70LoBfFcTMbyvZY4SSJZcIS41ILVMzBNby+kJGObzqY+QQmnPlDafJZE8FNW8XLwRD7G2Gzw7zaRRjRKr7mQTjdRKa48DwzRj6bQ1+WKcm17FK3l404NKzbxsn07HGQgrdFNOuCrYDcq9pCg/BSnM6DAdsb8KmwCCqbl9LqMX1LjITziKjkP3WnuUu0AlhiyrhXigvHLJ60xcKGDYZeoTuEmEKn80tiZYfDP8NRzEkKGuutQ4BYZihsgPPwTuKAOEJXSUv0yQB8RNIvwfENWUBO2j5SAIEvI7glsk0HmadRlmU3FRBZP6QP4rUZlWpAs1k1FEWiUGqMyajVMMSWXbi/XGpuYYv7GRSlAcwi0melDxyfZ0Tp21HzxJ67lfbybwaJC8++B8Mnol3m84V1luTcePJuGHvoH3bh9jbySk2+a0Gvec2Q3+WjB1ZDw/Q8SFZdY1Lz9UnWiR5nzqdZPGeLjr7T3krphzxpV4T8mE13BpS86qVx48Gfe4lH73YA8G3pZI619wz/4Q0z4fxjIJTHQhXNsU0sU5g98jCSDkugpyzi4lkEfiWF35ef3jEnU4EIaKgv0eieT2zX+qk/RvcUr1MzVRDJP7KoNELdVcIw33Mv6CPWoHmV7wO6OmTAh0JHgz9Y1k2uDn3Z6CzpTZWMXASFG2bLZ5MAF+qZn4NgUCMBXZJs/aw3Stc6+VpN8imjjja+853aab6APq3jQY0RURqWLC4T7+NZ491BV8+hJuztFsBz2EBk8isWMhxM7u63jx+t5H73LAQuDeH1EPMKDuC61K7LZBLA7ML4J43j1ko1ciPBeZPEwivRqKdy+OvYHTPyP7vMEmV1tPBswVhGazxcUD+S2OFPRztgfh8ZtaX8rMr/nmKnjte2yoMZXAcm7yguqp9fpNdQsmelob20IrLINjanO8nVhy8Nyattno0Wc3MT4VCd9Sprk5319jVIX9VEgCQQNQI+gl92soBFtfUBr1AQQlizs7KtTGfCjX5+HjX5aSLxeN1X+iOn+9TIQg3hvR/qWDXDQDL1FoH0naHUpfUSpw9n8N4lExdNAfeBTYCcTkNbEo303uWXmtgF4loyXoZxj4hCQ6sgQMFk/p5Nguhz70DSL3QdLn4JbnK1sBv3gJA2DYIX6cUjeOrrK/MOPw0wSmKhP547+8Ats9UMscmNK3Rpu5gjl/WlaQaONwFstoxxcizgQjZeICPL/AZeZMEg8W4Cazx3O40mR",
  'Content-Type': "application/json; charset=utf-8"
}

response = requests.post(url, data=payload, headers=headers)

print(response.text)
