#!/usr/bin/env python3
"""
简单的 SSE (Server-Sent Events) 客户端实现
功能：
1. 发起 SSE 请求
2. 接收 SSE 事件
3. 终止 SSE 连接
"""

import json
import requests
import signal
import sys
from typing import Optional, Dict, Any, Generator


class SSEClient:
    def __init__(self, url: str, headers: Optional[Dict[str, str]] = None, payload: Optional[Dict[str, str]] = None):
        """
        初始化 SSE 客户端

        Args:
            url: SSE 服务器的 URL
            headers: 请求头，默认为 None
        """
        self.url = url
        self.headers = headers or {}
        self.payload = payload or {}
        # 确保请求头中包含 SSE 所需的 Accept 头
        if 'Accept' not in self.headers:
            self.headers['Accept'] = 'text/event-stream'
        self.response = None
        self.running = False

    def connect(self) -> None:
        """建立 SSE 连接"""
        self.response = requests.post(
            self.url,
            headers=self.headers,
            data=json.dumps(self.payload),
            stream=True  # 流式传输
        )
        self.response.raise_for_status()  # 检查响应状态
        self.running = True

    def events(self) -> Generator[Dict[str, Any], None, None]:
        """
        生成器函数，用于接收和解析 SSE 事件

        Yields:
            解析后的 SSE 事件，格式为字典 {'event': event_type, 'data': data, 'id': id}
        """
        if not self.response:
            self.connect()

        data = ""
        event_type = ""
        event_id = ""

        for line in self.response.iter_lines(decode_unicode=True):
            if not self.running:
                break

            if not line:
                # 空行表示事件结束
                if data:
                    yield {
                        'event': event_type or 'message',
                        'data': data,
                        'id': event_id
                    }
                    data = ""
                    event_type = ""
                    event_id = ""
                continue

            if line.startswith('data:'):
                # 数据行
                data = line[5:].strip()
                data = data.encode("iso-8859-1")
                data = data.decode("utf-8")
                if data:
                    data += '\n'
            elif line.startswith('event:'):
                # 事件类型
                event_type = line[6:].strip()
            elif line.startswith('id:'):
                # 事件 ID
                event_id = line[3:].strip()
            elif line.startswith('retry:'):
                # 重试时间，这里简单忽略
                pass

    def close(self) -> None:
        """关闭 SSE 连接"""
        self.running = False
        if self.response:
            self.response.close()
            self.response = None


def main():
    """主函数，演示 SSE 客户端的使用"""
    url = "https://ai.yun.139.com/api/outer/assistant/chat/add"
    payload = {
    "userId": "1040004460754003037",
    "sessionId": "1214202033618291729",
    "content": {
        "dialogue": "9.11和 9.9 哪个大",
        "prompt": "",
        "timestamp": "2025-05-05 20:25:47",
        "commands": "",
        "resourceType": "0",
        "resourceId": "",
        "dialogueType": "0",
        "sourceChannel": "101",
        "extInfo": "{\"h5Version\":\"1.9.2\"}"
    },
    "applicationType": "chat",
    "applicationId": "",
    "enableForceNetworkSearch": False,
    "enableAllNetworkSearch": False
    }

    headers = {
        'User-Agent': "Mozilla/5.0 (Linux; Android 15; 23013RK75C Build/AQ3A.240912.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/135.0.7049.111 Mobile Safari/537.36 MCloudApp/11.5.0",
        'Accept': "text/event-stream",
        'Accept-Encoding': "gzip, deflate, br, zstd",
        'Content-Type': "application/json;charset=UTF-8",
        'sec-ch-ua-platform': "\"Android\"",
        'authorization': "Basic bW9iaWxlOjE4Mjc5NDkyNzYxOmVaQ2dPSUdifDF8UkNTfDE3NDkwMzczNDQ0Njl8TDJ0NnJBODNKRGswMTdWZmkzMGtUV0dLS3ZERHlrSHNyTF9YZFhjZ256YVVFWUdibE85Tmd4R3hlWVhKYXN1MTJieVh0VkJhYmozbnNmT0ZCZl9LQzBtVnlOOElOWmdwU2JXc19SM0E5bVIxQVc1c09ueGRteUNqaXFIbUhkTWlNR29aRXdTdlVOalliT1pWSnl4Ulg3QmpyZjZ5UXZhNU1LZnZub1ZaWmVJLQ==",
        'x-yun-client-info': "1||1|11.5.0||23013RK75C|7840B701DCEA5F145CCA26F1EAFE964D||android 15|||||",
        'sec-ch-ua': "\"Android WebView\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"",
        'sec-ch-ua-mobile': "?1",
        'x-yun-api-version': "v6",
        'x-yun-app-channel': "101",
        'x-yun-tid': "cb192d55-9bcb-4ee9-8acf-c3b059b6f717",
        'origin': "https://yun.139.com",
        'x-requested-with': "com.chinamobile.mcloud",
        'sec-fetch-site': "same-site",
        'sec-fetch-mode': "cors",
        'sec-fetch-dest': "empty",
        'referer': "https://yun.139.com/",
        'accept-language': "zh,zh-CN;q=0.9,en-US;q=0.8,en;q=0.7",
        'priority': "u=1, i"
    }
    client = SSEClient(url,headers=headers,payload=payload)

    # 注册信号处理，以便可以通过 Ctrl+C 终止
    def signal_handler(sig, frame):
        print("\n正在关闭 SSE 连接...")
        client.close()
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)

    print(f"正在连接到 SSE 服务器: {url}")
    print("按 Ctrl+C 终止连接")

    try:
        for event in client.events():
            event_type = event.get('event', 'message')
            data = event.get('data', '')
            event_id = event.get('id', '')

            print(f"事件类型: {event_type}")
            if event_id:
                print(f"事件 ID: {event_id}")
            print(f"数据: {data}")
            print("-" * 40)
    except requests.exceptions.RequestException as e:
        print(f"请求错误: {e}")
    finally:
        client.close()


if __name__ == "__main__":
    main()
