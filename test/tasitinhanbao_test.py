import requests
import json
#塔斯汀小程序

# 执行签到，获取请求体中的 memberPhone，获取请求头中的 user-token

TASITING_COOKIES="18279492761;sss2e6f11b4-7a3b-44ae-8118-658bd147d3e4"

cookies = TASITING_COOKIES.split(';')
member_phone = cookies[0]
user_token = cookies[1]

url = "https://sss-web.tastientech.com/api/sign/member/signV2"

payload = {
  "activityId": 60,
  "memberName": "",
  "memberPhone": member_phone
}

headers = {
  'User-Agent': "Mozilla/5.0 (Linux; Android 15; 23013RK75C Build/AQ3A.240912.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/134.0.6998.136 Mobile Safari/537.36 XWEB/1340123 MMWEBSDK/20250201 MMWEBID/8758 MicroMessenger/8.0.58.2841(0x28003A35) WeChat/arm64 Weixin NetType/WIFI Language/zh_CN ABI/arm64 MiniProgramEnv/android",
  'Content-Type': "application/json",
  'channel': "1",
  'version': "3.21.0",
  'user-token': user_token,
  'charset': "utf-8",
  'Referer': "https://servicewechat.com/wx557473f23153a429/427/page-frame.html"
}

response = requests.post(url, data=json.dumps(payload), headers=headers)

print(response.text)
