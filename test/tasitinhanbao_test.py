import requests
import json
#塔斯汀小程序

# 执行签到，获取请求体中的 memberPhone，获取请求头中的 user-token

TASITING_COOKIES="18279492761;sss2e6f11b4-7a3b-44ae-8118-658bd147d3e4"

cookies = TASITING_COOKIES.split(';')
member_phone = cookies[0]
user_token = cookies[1]

#1. 执行签到
url = "https://sss-web.tastientech.com/api/sign/member/signV2"

payload = {
  "activityId": 60,
  "memberName": "",
  "memberPhone": member_phone
}

headers = {
  'User-Agent': "Mozilla/5.0 (Linux; Android 15; 23013RK75C Build/AQ3A.240912.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/134.0.6998.136 Mobile Safari/537.36 XWEB/1340123 MMWEBSDK/20250201 MMWEBID/8758 MicroMessenger/8.0.58.2841(0x28003A35) WeChat/arm64 Weixin NetType/WIFI Language/zh_CN ABI/arm64 MiniProgramEnv/android",
  'Content-Type': "application/json",
  'channel': "1",
  'version': "3.21.0",
  'user-token': user_token,
  'charset': "utf-8",
  'Referer': "https://servicewechat.com/wx557473f23153a429/427/page-frame.html"
}

response = requests.post(url, data=json.dumps(payload), headers=headers)
#正常响应结果
# {
#   "code": 200,
#   "msg": null,
#   "traceId": "4cc0b4ad69c97278a8f2ebfa1dbb41d3",
#   "result": {
#     "continuousNum": 1,
#     "rewardInfo": null,
#     "rewardInfoList": [
#       {
#         "rewardId": null,
#         "bigType": null,
#         "rewardType": 5,
#         "rewardName": null,
#         "rewardPic": null,
#         "couponInfo": null,
#         "point": 5,
#         "thirdCouponId": null,
#         "otherInfo": null,
#         "dayNum": null,
#         "dateStr": null
#       }
#     ],
#     "map": null
#   }
# }
#已经签到响应结果
# {
#   "code": 500,
#   "msg": "您今天已经签过到了哦，明天再来吧",
#   "traceId": "ebe2c42b38fc45ee9a5aae6adf9912fa",
#   "result": null
# }


#2. 查询积分明细
url = "https://sss-web.tastientech.com/api/wx/point/myPoint"
response = requests.post(url, data=json.dumps({}), headers=headers)
# {
#   "code": 200,
#   "msg": null,
#   "traceId": "2f8db51f02311c31a83016191e69c985",
#   "result": {
#     "point": 10,
#     "rule": {
#       "name": "积分",
#       "dailyLimit": null,
#       "validPeriodType": 3,
#       "validPeriodParams": "{\"month\": 6}"
#     },
#     "customizeRules": [
#       {
#         "triggerType": 2,
#         "ruleParams": "{\"count\": 1, \"amount\": 1}"
#       }
#     ]
#   }
# }


#3. 获取接下来的奖励列表
url = "https://sss-web.tastientech.com/api/sign/member/signInfoV2"
response = requests.post(url, data=json.dumps({}), headers=headers)
# {
#   "code": 200,
#   "msg": null,
#   "traceId": "b605bf5f4eaa4bc3ae27614ecf5b8cbe",
#   "result": {
#     "activityInfo": {
#       "rewardList": [
#         {
#           "rewardId": 195,
#           "bigType": 2,
#           "rewardType": 1,
#           "rewardName": "3元无门槛代金券",
#           "rewardPic": "https://pictures.tastientech.com/tastienonline//1732758457393/%E7%94%BB%E6%9D%BF%2030%406x-100.jpg?Expires=3309558457&OSSAccessKeyId=LTAI5tAMWY4mLneSyCjMtiHz&Signature=wDKDq8SAgehfKUB8f8JoN2CVCtg%3D",
#           "couponInfo": [
#             {
#               "templateId": 9518,
#               "num": 1,
#               "name": "积分签到-3元无门槛代金券",
#               "couponContent": "3.00元",
#               "couponTime": "领券当日起7日内可用"
#             }
#           ],
#           "point": 0,
#           "thirdCouponId": 0,
#           "otherInfo": null,
#           "dayNum": 2,
#           "dateStr": "20250613"
#         },
#         {
#           "rewardId": 196,
#           "bigType": 2,
#           "rewardType": 2,
#           "rewardName": "7积分",
#           "rewardPic": "https://pictures.tastientech.com/tastienonline//1732758477270/%E7%A7%AF%E5%88%86%E5%95%86%E5%9F%8E%E4%BC%98%E5%8C%96-%E6%BA%90%E6%96%87%E4%BB%B6-47.jpg?Expires=3309558477&OSSAccessKeyId=LTAI5tAMWY4mLneSyCjMtiHz&Signature=gRZoyvEgC1ozHpvjw%2BLzMaiSE2I%3D",
#           "couponInfo": null,
#           "point": 7,
#           "thirdCouponId": 0,
#           "otherInfo": null,
#           "dayNum": 3,
#           "dateStr": "20250614"
#         },
#         {
#           "rewardId": 197,
#           "bigType": 2,
#           "rewardType": 2,
#           "rewardName": "7积分",
#           "rewardPic": "https://pictures.tastientech.com/tastienonline//1732758494170/%E7%A7%AF%E5%88%86%E5%95%86%E5%9F%8E%E4%BC%98%E5%8C%96-%E6%BA%90%E6%96%87%E4%BB%B6-47.jpg?Expires=3309558494&OSSAccessKeyId=LTAI5tAMWY4mLneSyCjMtiHz&Signature=%2BKikqoE7SLwaeyIbmj3cub%2BtMEs%3D",
#           "couponInfo": null,
#           "point": 7,
#           "thirdCouponId": 0,
#           "otherInfo": null,
#           "dayNum": 4,
#           "dateStr": "20250615"
#         },
#         {
#           "rewardId": 198,
#           "bigType": 2,
#           "rewardType": 1,
#           "rewardName": "荆楚尖叫翅尖买一送一",
#           "rewardPic": "https://pictures.tastientech.com/tastienonline//1732758513801/%E7%A7%AF%E5%88%86%E5%95%86%E5%9F%8E%E4%BC%98%E5%8C%96-%E6%BA%90%E6%96%87%E4%BB%B6-49.jpg?Expires=3309558513&OSSAccessKeyId=LTAI5tAMWY4mLneSyCjMtiHz&Signature=FLHZE%2F5AQtcp7xRvaubHG7H%2F%2Fzk%3D",
#           "couponInfo": [
#             {
#               "templateId": 10173,
#               "num": 1,
#               "name": "积分签到-荆楚尖叫翅尖买一送一",
#               "couponContent": "买2份其中1份特价0元",
#               "couponTime": "领券当日起7日内可用"
#             }
#           ],
#           "point": 0,
#           "thirdCouponId": 0,
#           "otherInfo": null,
#           "dayNum": 5,
#           "dateStr": "20250616"
#         },
#         {
#           "rewardId": 199,
#           "bigType": 2,
#           "rewardType": 2,
#           "rewardName": "10积分",
#           "rewardPic": "https://pictures.tastientech.com/tastienonline//1732758529295/%E7%A7%AF%E5%88%86%E5%95%86%E5%9F%8E%E4%BC%98%E5%8C%96-%E6%BA%90%E6%96%87%E4%BB%B6-48.jpg?Expires=3309558529&OSSAccessKeyId=LTAI5tAMWY4mLneSyCjMtiHz&Signature=vIKpfibxglzZMAV7tTBZV5SYpcM%3D",
#           "couponInfo": null,
#           "point": 10,
#           "thirdCouponId": 0,
#           "otherInfo": null,
#           "dayNum": 6,
#           "dateStr": "20250617"
#         },
#         {
#           "rewardId": 200,
#           "bigType": 2,
#           "rewardType": 1,
#           "rewardName": "0元香辣鸡腿中国汉堡券",
#           "rewardPic": "https://pictures.tastientech.com/tastienonline//1732758562686/%E7%94%BB%E6%9D%BF%2030%20%E5%89%AF%E6%9C%AC%406x-100.jpg?Expires=3309558562&OSSAccessKeyId=LTAI5tAMWY4mLneSyCjMtiHz&Signature=fyyMSjeQVqxLt8OxbFcigMddFYE%3D",
#           "couponInfo": [
#             {
#               "templateId": 9519,
#               "num": 1,
#               "name": "积分签到-0元香辣鸡腿中国汉堡券",
#               "couponContent": "特价0.00元",
#               "couponTime": "领券当日起7日内可用"
#             }
#           ],
#           "point": 0,
#           "thirdCouponId": 0,
#           "otherInfo": null,
#           "dayNum": 7,
#           "dateStr": "20250618"
#         }
#       ]
#     }
#   }
# }

print(response.text)
