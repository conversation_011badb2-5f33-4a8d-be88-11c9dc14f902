import base64
import random
import string
import json
def parse_to_offset(data):
    # 获取加密字符串的随机部分，并存入cookie
    encrypted_split = cookie_set(random_split(data))

    # 从cookie中获取存储的随机加密字符串
    cookie_value = cookie_get(encrypted_split)

    # 通过cookie值获取偏移量
    offset_value = get_offset2(cookie_value)
    return offset_value

# 以下为相关辅助函数的假设实现（仅示例）
def random_split(encrypt_data):
    """
    将输入字符串按照逗号进行分割，提取每个分割项中的第二部分（以冒号分割），
    然后将提取的部分用 'd' 连接成一个新的字符串。

    参数:
    encrypt_data (str): 输入的加密字符串，格式为 "key1:value1,key2:value2,..."

    返回:
    str: 提取后的字符串，各部分用 'd' 连接。
    """
    # 先按逗号分割字符串，再对每个部分按冒号分割，取第二部分，用'd'连接
    return 'd'.join([part.split(":")[1] for part in encrypt_data.split(",")])

def cookie_set(data):
    """
    将输入字符串的每个字符转换为其对应的ASCII值，并与随机生成的字符串组合，
    然后用 '-' 连接成一个新的字符串。

    参数:
    data (str): 输入的字符串。

    返回:
    str: 处理后的字符串，字符的ASCII值与随机字符串组合。
    """
    # 将输入字符串拆分成字符列表
    char_list = list(data)

    # 对每个字符进行处理，将其ASCII值和随机字符串组合
    processed_chars = [
        f"{ord(char)}-{random_string()}" for char in char_list
    ]

    # 将处理后的字符列表用'-'连接成一个字符串
    return "-".join(processed_chars)

def random_string():
    """
    生成一个包含两位小写字母和数字的随机字符串。

    返回:
    str: 随机生成的两位字符串。
    """
    return ''.join(random.choices(string.ascii_lowercase + string.digits, k=2))

def cookie_get(data):
    """
    从输入的经过编码的字符串中提取出每个字符的 ASCII 值，并将这些 ASCII 值转换为字符，
    最终组合成原始的字符串。

    参数:
    data (str): 输入的经过编码的字符串，格式为 "ASCII-随机串-ASCII-随机串-...".

    返回:
    str: 解码后的原始字符串。
    """
    # 如果输入数据存在，则按 '-' 分割，否则返回空列表
    split_data = data.split("-") if data else []

    # 过滤出原始的ASCII值部分（偶数位置的元素），并将其转换为字符
    ascii_values = [int(split_data[i]) for i in range(0, len(split_data), 2)]

    # 将ASCII值列表转换为字符串
    return ''.join([chr(value) for value in ascii_values])

def get_offset2(data):
    """
    处理输入字符串，将其按 'd' 分割，转换为整数后，调用 get_offset 方法
    来计算偏移量。

    参数:
    data (str): 输入的字符串，格式为 "num1dnum2dnum3...".

    返回:
    int: 计算得到的偏移量。
    """
    # 将输入字符串按 'd' 分割，转换为整数
    numbers = [int(part) for part in data.split("d")]

    # 调用 get_offset 函数，传入分割后的整数列表（去掉第一个元素）
    return get_offset(*numbers[1:])

def get_offset(e, t, i):
    """
    计算偏移量，根据公式 (e | t) % 256 + i。

    参数:
    e (int): 第一个整数。
    t (int): 第二个整数。
    i (int): 第三个整数。

    返回:
    int: 计算得到的偏移量。
    """
    return (e | t) % 256 + i

def offset_out(offset, base64_string):
    """
    对 Base64 编码的字符串进行解码，并通过给定的 offset 偏移量恢复原始字符。

    参数:
    offset (int): 用来解码的偏移量，与编码时使用的偏移量相同。
    base64_string (str): 经过 Base64 编码的字符串。

    返回:
    str: 解码并恢复的原始字符串。
    """
    # 从 Base64 字符串解码为字节数组
    byte_array = base64.b64decode(base64_string)
    # 用于存储恢复后的 ASCII 值
    ascii_data = []
    # 对每个字节进行偏移运算，恢复原始的字节值
    for byte in byte_array:
        ascii_data.append((byte - offset) % 256)
    # 将 ASCII 值转换为字符并拼接成字符串
    return ''.join(chr(num) for num in ascii_data)

def offset_in(offset, byte_array):
    """
    将输入的字节数组 `t` 中的每个字节加上整数 `e`，并对 256 取模，然后将结果编码为 Base64。

    参数:
    offset (int): 要加到每个字节上的整数。
    byte_array (bytes): 输入的字节数组。

    返回:
    str: 处理后的字节数组，经过 Base64 编码后的字符串。
    """
    # 将输入的字节数据转换为可修改的列表
    byte_array = bytearray(byte_array.encode('utf-8'))

    # 对每个字节加上整数 e，然后对 256 取模
    for n in range(len(byte_array)):
        byte_array[n] = (byte_array[n] + offset) % 256

    # 将处理后的字节数组编码为 Base64 字符串
    encoded_result = base64.b64encode(bytes(byte_array)).decode('utf-8')

    return encoded_result


def get_resp(offset,token):
    import requests

    # url = "https://n.cg.163.com/api/v2/ads/get_info"

    # params = {
    # #'device_id': "ef77c58f82121928",
    # 'ads_platform_idx': "1",
    # 'scene_value': "sign_ads"
    # }
    url = "https://n.cg.163.com/api/v2/users/@me"
    # url = "https://n.cg.163.com/api/v2/users/@current"
    # url = "https://n.cg.163.com/api/v2/sign-user-info"
    # url = "https://n.cg.163.com/api/v2/users/@me/cloud-pc"
    # url = "https://n.cg.163.com/api/v2/users/@me/level_info"
    # url = "https://n.cg.163.com/api/v2/client-settings/@current"
    # url = "https://n.cg.163.com/api/v2/daily_vip/get_daily_vip_reward_status"
    # url = "https://n.cg.163.com/api/v2/daily_vip/get_user_daily_vip_rewards"
    # url = "https://n.cg.163.com/api/v2/get_user_ultimate_game_info"

    headers = {
        'User-Agent': "NetEaseCloudGame/2.8.8 (versionName:*******;versionCode:2360;channel:xiaomi_new;sdk:34;device:Xiaomi,mondrian,23013RK75C;)",
        'Connection': "Keep-Alive",
        'Accept': "application/json",
        'Accept-Encoding': "gzip",
        'Authorization': token,
        'X-Channel': "xiaomi_new",
        'X-Ver': "2360",
        'X-Source-Type': "xiaomi_new",
        'X-Platform': "2",
        'Content-Type': "application/octet-stream"
    }
    response = requests.get(url, headers=headers)
    info = json.loads(offset_out(offset,response.text))
    print(info)
def get_response_ad(offset, token):
    import requests

    # url = "https://n.cg.163.com/api/v2/ads/get_info"

    # params = {
    # #'device_id': "ef77c58f82121928",
    # 'ads_platform_idx': "1",
    # 'scene_value': "sign_ads"
    # }
    url = "https://n.cg.163.com/api/v2/users/@me"

    headers = {
        'User-Agent': "NetEaseCloudGame/2.8.8 (versionName:*******;versionCode:2360;channel:xiaomi_new;sdk:34;device:Xiaomi,mondrian,23013RK75C;)",
        'Connection': "Keep-Alive",
        'Accept': "application/json",
        'Accept-Encoding': "gzip",
        'Authorization': token,
        'X-Channel': "xiaomi_new",
        'X-Ver': "2360",
        'X-Source-Type': "xiaomi_new",
        'X-Platform': "2",
        'Content-Type': "application/octet-stream"
    }

    # {'ads_id': '15031511', 'ads_platform': 'ubix', 'ads_type': 'reward_video', 'device_times': 3, 'display': True, 'is_last_time_range': False, 'is_time_ranged': False, 'limit_times': 8, 'scene_value': 'sign_ads', 'time_range_limit_times': 0, 'time_range_user_times': 0, 'user_times': 3}
    # iL3dLOcz
    # uF+horOmoKKcpqFfd1+io3R0oHJ1o3Vvbm9udm91X2lfr56roayqnLGitbFfd1+miXChiYygt19pX56hsJymoV93X25ybXBucm5uX2lfnqGwnK2pnrGjrK+qnKahtV93bmlfsKCiq6Kcs56psqJfd1+wpqSrnJ6hsF9pX7CxnrGysF93bro=
    # {"ads_info":{"ads_id":"15031511","ads_platform":"ubix","ads_type":"reward_video","device_times":4,"display":true,"is_last_time_range":false,"is_time_ranged":false,"limit_times":8,"scene_value":"sign_ads","time_range_limit_times":0,"time_range_user_times":0,"user_times":4},"bonus_text":"\u7b2c5\u6b21\u89c2\u770b\u53ef\u83b7\u5f97\u7ffb\u500d\u65f6\u957f","bonus_time":5,"msg":"\u606d\u559c\u4f60\uff0c\u62bd\u4e2d2\u5206\u949f\u624b\u6e38\u65f6\u957f\uff01\u5f53\u5929\u53ef\u770b8\u6b21\u5662~\u524d\u5f80\u798f\u5229\u4e2d\u5fc3\u770b\u5e7f\u544a\u80fd\u62bd\u53d6\u66f4\u591a\u65f6\u957f\u54e6","pop_stay_time":3,"reward_status":0,"reward_type":"mobile_time","reward_val":120,"toast_to_popup":true}
    #获取广告信息

    response = requests.get(url, params=params, headers=headers)
    ad_info = json.loads(offset_out(offset,response.text))
    print("看广告")
    print(f"共 {ad_info['limit_times']} 次机会")
    print(f"已经使用 {ad_info['user_times']} 次")

    #获取广告完成密钥
    url = "https://n.cg.163.com/api/v2/one_submit_ticket"
    params = {
    'business_type': "1"
    }

    response = requests.get(url, params=params, headers=headers)
    random_text = json.loads(offset_out(offset,response.text))["random_text"]
    print(f"广告随机码：{random_text}")

    url = "https://n.cg.163.com/api/v2/ads/give_ad_reward"

    # tt = '{"device_id":"ef77c58f82121928","random_text":"79jy0cZ5","ads_id":"15031511","ads_platform_idx":1,"scene_value":"sign_ads","status":1}'
    # payload = "uF+horOmoKKcpqFfd1+io3R0oHJ1o3Vvbm9udm91X2lfr56roayqnLGitbFfd190dqe2baCXcl9pX56hsJymoV93X25ybXBucm5uX2lfnqGwnK2pnrGjrK+qnKahtV93bmlfsKCiq6Kcs56psqJfd1+wpqSrnJ6hsF9pX7CxnrGysF93bro="
    # payload = "uF+horOmoKKcpqFfd1+io3R0oHJ1o3Vvbm9udm91X2lfr56roayqnLGitbFfd1+miXChiYygt19pX56hsJymoV93X25ybXBucm5uX2lfnqGwnK2pnrGjrK+qnKahtV93bmlfsKCiq6Kcs56psqJfd1+wpqSrnJ6hsF9pX7CxnrGysF93bro="

    device_id = "ef77c58f82121928"
    ori_payload = {
        #"device_id": "ef77c58f82121928",
        "random_text": random_text,
        "ads_id": "15031511",
        "ads_platform_idx": 1,
        "scene_value": "sign_ads",
        "status": 1
    }
    ori_payload_str = json.dumps(ori_payload,separators=(",",":"))
    payload = offset_in(offset,ori_payload_str)
    #print(payload)
    #sssss = '{"ads_info":{"ads_id":"15031511","ads_platform":"ubix","ads_type":"reward_video","device_times":4,"display":true,"is_last_time_range":false,"is_time_ranged":false,"limit_times":8,"scene_value":"sign_ads","time_range_limit_times":0,"time_range_user_times":0,"user_times":4},"bonus_text":"\u7b2c5\u6b21\u89c2\u770b\u53ef\u83b7\u5f97\u7ffb\u500d\u65f6\u957f","bonus_time":5,"msg":"\u606d\u559c\u4f60\uff0c\u62bd\u4e2d2\u5206\u949f\u624b\u6e38\u65f6\u957f\uff01\u5f53\u5929\u53ef\u770b8\u6b21\u5662~\u524d\u5f80\u798f\u5229\u4e2d\u5fc3\u770b\u5e7f\u544a\u80fd\u62bd\u53d6\u66f4\u591a\u65f6\u957f\u54e6","pop_stay_time":3,"reward_status":0,"reward_type":"mobile_time","reward_val":120,"toast_to_popup":true}'
    #sssss_o = json.loads(sssss)
    response = requests.post(url, data=payload, headers=headers)
    ad_result = json.loads(offset_out(offset, response.text))
    print(f"看广告获得 {ad_result.get('reward_val')/60} 分钟时长")
    print(ad_result)
# 示例用法
if __name__ == "__main__":

    # 示例字节数组
    #byte_array = 'SvEyREFBND1DLkM4PDTxCQAGAQYFBwD/BQX78TYwPDQuOzg8OEMuPD4xODs0LkU4P/EJSvE4Mj498QnxN0NDP0IJ/v42/TU//T9C/T00QzQwQjT9Mj48/jI2/Dg8MDY0/jU4OzT+BQEwBQg0BjQCBTIyAzEHBQMC/wQ1AQcCKBQYKTtEKAT/A/H78T0wPDTxCfErRAUBAzErRAU0AgcrRAgwMwcrRAgAAzMrRAM1ADArRAQDBAfxTEzZ'

    #print(handle_response('uF+horOmoKKcpqFfd1+io3R0oHJ1o3Vvbm9udm91X2lfr56roayqnLGitbFfd1+mdoOQln+Scl9pX56hsJymoV93X25ybXBucm5uX2lfnqGwnK2pnrGjrK+qnKahtV93bmlfsKCiq6Kcs56psqJfd1+wpqSrnJ6hsF9pX7CxnrGysF93bro='))
    #
    # print(handle_response('uF+eobCcpqFfd19ucm1wbnJub19pX56hsJytqZ6xo6yvql93X7KfprVfaV+eobCcsbatol93X6OioqFfukc='))
    # print(handle_response('uF+vnquhrKqcsaK1sV93X3R2p7ZtoJdyX7pH'))
    # print(handle_response('uF+horOmoKKcpqFfd1+io3R0oHJ1o3Vvbm9udm91X2lfr56roayqnLGitbFfd190dqe2baCXcl9pX56hsJymoV93X25ybXBucm5uX2lfnqGwnK2pnrGjrK+qnKahtV93bmlfsKCiq6Kcs56psqJfd1+wpqSrnJ6hsF9pX7CxnrGysF93bro='))
    # print(json.loads(offset_out(317,'uF+hnrGeX3e4umlfoq+voKyhol93bnBtcGlfoq+vqrCkX3dfq6KioV2iq6Cvtq2xpqyrX2lfoq+vqrCkoKtfd1+ZsnZ0bW2ZsnV2dW6ZsnJvnm2ZsnKfoHNfukc=')))
    #data = json.loads(offset_out(317, 'mLhfnqCgpqFfd19zcKBvcnRtdnahcaNybnago3ajbXaenqJfaV+es56xnq9fd6uyqalpX56znrGer5yjr56qopyyr6lfd19faV+rpqCoq56qol93X5mydHJvdZmyc29wdJ6enp50lbKnpl9pX6umoKirnqqinKCsqayvX3dfX2lfsrCir5ymoV93X3NwoG9ydG12dqFxo3JudqCjdqNtdp6eol9pX7Kwoq+cr6KpX3dxuppH'))
    ##print(data)
    encrypt_data = "method:1,number:298255574,timestamp:1729593675,salt:94"
    token = "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3Mjk1OTM2NzUsIm5iZiI6MTcyOTU5MzY3NSwianRpIjoiM2I2NmU5NDYtN2Y4ZC00NWIyLTg0ZTItMmM5NmE4NmU1MzJlIiwiaWRlbnRpdHkiOiI2M2MyNTcwOTlkNGY1MTljZjlmMDlhYWUiLCJmcmVzaCI6ZmFsc2UsInR5cGUiOiJhY2Nlc3MiLCJ1c2VyX2NsYWltcyI6eyJudW1iZXIiOjI5ODI1NTU3NCwidGltZXN0YW1wIjoxNzI5NTkzNjc1LCJzYWx0Ijo5NH19.rAa7ng3Fa3IKrllTcrN5hR4r9kjgb1OmUNHC9P_1T_w"
    offset = parse_to_offset(encrypt_data)
    # get_response_ad(offset, token)
    get_resp(offset, token)
