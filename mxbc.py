# !/usr/bin/python3
# -- coding: utf-8 --
# -------------------------------
# 蜜雪冰城小程序
# <AUTHOR>
# @Time 2024.09.01
# @Description
# ✨ 功能：
#     蜜雪冰城小程序签到获取积分
# ✨ 抓包步骤：
#     打开抓包工具
#     打开蜜雪冰城小程序
#     授权登陆
#     找 https://mxsa.mxbc.net/api/v1/app/loginByUnionid 的URl(如果已经授权登陆先退出登陆)
#     复制里面的 unionid 参数值
# ✨ 变量示例：
#     export MXBC_UNIONID='o0GLxx'参数值，多账号换行分割
# -------------------------------
# cron "30 9 * * *" script-path=xxx.py,tag=匹配cron用
# const $ = new Env('蜜雪冰城小程序')
# -------------------------------

from tools.common import BaseRun
#from typing import override
from urllib.parse import quote_plus
from Crypto.PublicKey import RSA
from Crypto.Signature import PKCS1_v1_5
from Crypto.Hash import SHA256
import base64
import requests
import json
import os
import time

class Run(BaseRun):
    #@override
    def init_vars(self):
        self.session = requests.Session()
        self.session.verify = False
        # self.session.proxies = {
        #     'http': 'http://**********:7890',
        #     'https': 'http://**********:7890'
        # }
        self.base_url = 'https://mxsa.mxbc.net'
        self.app_id = 'd82be6bbc1da11eb9dd000163e122ecb'
        #剩余时长
        self.headers = {
            'Host': 'mxsa.mxbc.net',
            'Content-Type': 'application/json',
            'xweb_xhr': '1',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309080f) XWEB/9105',
            'version': '2.2.5',
            'Accept': '*/*',
            'Sec-Fetch-Site': 'cross-site',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': 'https://servicewechat.com/wx7696c66d2245d107/105/page-frame.html',
            'Accept-Language': 'zh-CN,zh;q=0.9'
        }
        self.access_token = ''
        #用户信息
        self.mobile_phone = ''
        self.before_point = 0
        self.after_point = 0
        #本次执行签到获得的雪王币
        self.rule_value_point = 0
    #@override
    def process_vars(self,info):
        self.unionid = info
        self.session.headers.update(self.headers)
    #@override
    def process(self, info, _):
        self.logger.info("===>🆕 变量准备")
        self.process_vars(info)
        self.logger.info("===>🔛 登录账号")
        if self.login():
            self.logger.info("===>💥 签到")
            self.signin()
            self.logger.info("===>🧑 获取用户信息")
            self.get_user_info()
            self.logger.final(f"雪王币变化：{self.before_point} --> {self.after_point}\n本次签到获得：{self.rule_value_point}")

    def get_sign(self,params):
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        sorted_params = sorted(params.items())
        param_str = "&".join(
            f"{k}={quote_plus(json.dumps(v)) if isinstance(v, dict) else quote_plus(str(v))}" for k, v in sorted_params)
        key = RSA.importKey(privateKeyString)
        # 创建一个SHA256哈希对象
        hash_obj = SHA256.new(param_str.encode())
        # 创建一个签名者
        signer = PKCS1_v1_5.new(key)
        # 对哈希对象进行签名
        signature = signer.sign(hash_obj)
        # 对签名进行Base64编码，并替换"/"和"+"字符
        signature = base64.b64encode(signature).decode()
        signature = signature.replace("/", "_").replace("+", "-")
        # print(signature)
        return signature

    def login(self):
        params = {
            'third': 'wxmini',
            'unionid': self.unionid,
            't': int(time.time() * 1000),  # 获取当前13位时间戳
            'appId': self.app_id
        }
        sign = self.get_sign(params)
        params['sign'] = sign
        response = self.session.post(f'{self.base_url}/api/v1/app/loginByUnionid', json=params)
        if response.status_code == 200:
            res_json = response.json()
            code = res_json.get('code', None)
            if code == 0:
                data = res_json.get('data', {})
                access_token = data.get('accessToken', '')
                if access_token:
                    self.access_token = access_token
                    self.mobile_phone = data.get('mobilePhone', '')
                    self.before_point = data['customerInfo']['customerPoint']
                    self.session.headers['Access-Token'] = access_token
                    self.logger.info(f'✅ 用户 {self.mobile_phone} 登陆成功')
                    return True
                else:
                    self.logger.error('❌ 登录失败，accessToken未找到。')
                    return False
            else:
                self.logger.error('❌ 登录请求未成功😢，返回的code不为0')
                return False
        else:
            self.logger.error('❌ 登录请求失败😢，状态码：', response.status_code)
            self.logger.error('❌ 响应内容：', response.text)
            return False
    def get_user_info(self):
        url = f"{self.base_url}/api/v1/customer/info"
        params = {
            't': int(time.time() * 1000),
            'appId': self.app_id,
        }
        params['sign'] = self.get_sign(params)
        result_json = self.session.get(url, params=params).json()
        if result_json.get("data", None):
            self.after_point = result_json['data']['customerPoint']
        else:
            self.logger.info('❌ 获取用户信息请求未成功😢')
    def signin(self):
        params = {
            't': int(time.time() * 1000),  # 获取当前13位时间戳
            'appId': self.app_id
        }
        params['sign'] = self.get_sign(params)
        response = self.session.get(f'{self.base_url}/api/v1/customer/signin', params=params)
        if response.status_code == 200:
            res_json = response.json()
            code = res_json.get('code', None)
            if code == 0:
                data = res_json.get('data', {})
                self.rule_value_point = data.get('ruleValuePoint', '')
                self.logger.info(f'✅ 签到成功，本次获得：【{self.rule_value_point}】雪王币')
            elif code == 5020 :
                self.logger.info('ℹ️  今日已经签到过了')
            else:
                self.logger.error('❌ 签到请求未成功😢，返回的code不为0')
        else:
            self.logger.error('❌ 签到请求失败😢，状态码：', response.status_code)
            self.logger.error('❌ 响应内容：', response.text)
if __name__ == '__main__':
    app_name = "蜜雪冰城小程序"
    app_env_name = "MXBC_UNIONID"
    local_script_name = os.path.basename(__file__)
    local_version = '2025.01.11'
    run = Run(app_name=app_name,
              app_env_name=app_env_name,
              script_version=local_version)
    run.main()
