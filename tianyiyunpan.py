# !/usr/bin/python3
# -- coding: utf-8 --
# -------------------------------
# 天翼云盘
# <AUTHOR>
# @Time 2024.09.16
# @Description
# ✨ 功能：
#     天翼云盘签到，抽奖
# ✨ 设置青龙变量：
#     export TYYP_PHONE='18888888888' 多账号，每个手机号换行分割
#     export TYYP_PASSWORD='18888888888' 多账号，每个密码换行分割，并与手机号一一对应
# -------------------------------
# cron "23 9 * * *" script-path=xxx.py,tag=匹配cron用
# const $ = new Env('天翼云盘签到');
# -------------------------------
from tools.common import BaseRun
#from typing import override
import base64
import os
import re
import time
import requests
import rsa
class Run(BaseRun):
    #@override
    def init_vars(self):
        self.session = requests.Session()
        self.b64map = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"
        self.UA = 'Mozilla/5.0 (Linux; Android 5.1.1; SM-G930K Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/74.0.3729.136 Mobile Safari/537.36 Ecloud/8.6.3 Android/22 clientId/355325117317828 clientModel/SM-G930K imsi/460071114317824 clientChannelId/qq proVersion/1.0.6'
        self.headers = {
            'User-Agent': self.UA,
            "Referer": "https://m.cloud.189.cn/zhuanti/2016/sign/index.jsp?albumBackupOpened=1",
            "Host": "m.cloud.189.cn",
            "Accept-Encoding": "gzip, deflate",
            #"Accept": "application/json;charset=UTF-8"
        }
        self.free_size = 0
        self.total_size = 0
        self.used_size = 0
    #@override
    def process_vars(self, info):
        self.username,self.password = info.get("TYYP_PHONE"), info.get("TYYP_PASSWORD")
        #self.session.headers.update(self.headers)
    #@override
    def process(self, info, _):
        self.logger.info("===>🆕 变量准备")
        self.process_vars(info)
        self.logger.info("===>🧑 登陆账号")
        if not self.login():
            return False
        self.logger.info("===>💥 签到")
        self.signin()
        self.logger.info("===>🎁 抽奖")
        self.lottery()
        self.info()
        self.logger.final(f"✅ 获取用户信息成功！\n"
                        f"✅ 已用：{self.used_size}G\n"
                        f"✅ 总共：{self.total_size}G\n"
                        f"✅ 剩余：{self.free_size}G")
    @staticmethod
    def int2char(a):
        return list("0123456789abcdefghijklmnopqrstuvwxyz")[a]

    def b64tohex(self, a):
        d = ""
        e = 0
        c = 0
        for i in range(len(a)):
            if list(a)[i] != "=":
                v = self.b64map.index(list(a)[i])
                if e == 0:
                    e = 1
                    d += self.int2char(v >> 2)
                    c = 3 & v
                elif e == 1:
                    e = 2
                    d += self.int2char(c << 2 | v >> 4)
                    c = 15 & v
                elif e == 2:
                    e = 3
                    d += self.int2char(c)
                    d += self.int2char(v >> 2)
                    c = 3 & v
                else:
                    e = 0
                    d += self.int2char(c << 2 | v >> 4)
                    d += self.int2char(15 & v)
        if e == 1:
            d += self.int2char(c << 2)
        return d

    def rsa_encode(self, j_rsakey, string):
        rsa_key = f"-----BEGIN PUBLIC KEY-----\n{j_rsakey}\n-----END PUBLIC KEY-----"
        pubkey = rsa.PublicKey.load_pkcs1_openssl_pem(rsa_key.encode())
        return self.b64tohex(
            (base64.b64encode(rsa.encrypt(f"{string}".encode(), pubkey))).decode()
        )

    def login(self):
        urlToken = "https://m.cloud.189.cn/udb/udb_login.jsp?pageId=1&pageKey=default&clientType=wap&redirectURL=https://m.cloud.189.cn/zhuanti/2021/shakeLottery/index.html"
        r = self.session.get(urlToken)
        pattern = r"https?://[^\s'\"]+"  # 匹配以http或https开头的url
        match = re.search(pattern, r.text)  # 在文本中搜索匹配
        if match:  # 如果找到匹配
            url = match.group()  # 获取匹配的字符串
        else:  # 如果没有找到匹配
            self.logger.error("❌ 没有找到url")
        r = self.session.get(url)
        pattern = r"<a id=\"j-tab-login-link\"[^>]*href=\"([^\"]+)\""  # 匹配id为j-tab-login-link的a标签，并捕获href引号内的内容
        match = re.search(pattern, r.text)  # 在文本中搜索匹配
        if match:  # 如果找到匹配
            href = match.group(1)  # 获取捕获的内容
            r = self.session.get(href)
        else:  # 如果没有找到匹配
            self.logger.error("❌ 没有找到 href 链接")
            exit()
        captchaToken = re.findall(r"captchaToken' value='(.+?)'", r.text)[0]
        lt = re.findall(r'lt = "(.+?)"', r.text)[0]
        returnUrl = re.findall(r"returnUrl= '(.+?)'", r.text)[0]
        paramId = re.findall(r'paramId = "(.+?)"', r.text)[0]
        j_rsakey = re.findall(r'j_rsaKey" value="(\S+)"', r.text, re.M)[0]
        self.session.headers.update({"lt": lt})
        username = self.rsa_encode(j_rsakey, self.username)
        password = self.rsa_encode(j_rsakey, self.password)
        url = "https://open.e.189.cn/api/logbox/oauth2/loginSubmit.do"
        headers = {
            'User-Agent': self.UA,
            'Referer': 'https://open.e.189.cn/',
        }
        data = {
            "appKey": "cloud",
            "accountType": '01',
            "userName": f"{{RSA}}{username}",
            "password": f"{{RSA}}{password}",
            "validateCode": "",
            "captchaToken": captchaToken,
            "returnUrl": returnUrl,
            "mailSuffix": "@189.cn",
            "paramId": paramId
        }
        r = self.session.post(url, data=data, headers=headers, timeout=5)
        if (r.json()['result'] == 0):
            self.logger.info('✅ 登陆成功！')
        else:
            self.logger.error(f"❌ 登陆失败！：{r.json()['msg']}")
            return False
        redirect_url = r.json()['toUrl']
        r = self.session.get(redirect_url)
        if r.status_code == 200:
            return r
        else:
            self.logger.error("❌ 登陆失败！")
            return False

    def signin(self):
        rand = str(round(time.time() * 1000))
        surl = f'https://api.cloud.189.cn/mkt/userSign.action?rand={rand}&clientType=TELEANDROID&version=8.6.3&model=SM-G930K'
        response = self.session.get(surl, headers=self.headers)
        netdiskBonus = response.json()['netdiskBonus']
        if not response.json().get('isSign',False):
            self.logger.info(f"✅ 签到成功，签到获得 {netdiskBonus}M 空间")
        else:
            self.logger.info(f"⚠️  已经签到过了，签到获得 {netdiskBonus}M 空间")

    def lottery(self):
        lottery_base_url = 'https://m.cloud.189.cn'
        url_list = [f'{lottery_base_url}/v2/drawPrizeMarketDetails.action?taskId=TASK_SIGNIN&activityId=ACT_SIGNIN',
                    # f'{lottery_base_url}/v2/drawPrizeMarketDetails.action?taskId=TASK_2022_FLDFS_KJ&activityId=ACT_SIGNIN',
                    # f'{lottery_base_url}/v2/drawPrizeMarketDetails.action?taskId=TASK_SIGNIN_PHOTOS&activityId=ACT_SIGNIN',
                    ]
        for index, urls in enumerate(url_list):
            response = self.session.get(urls, headers=self.headers)
            if ("errorCode" in response.text):
                self.logger.error(f"❌ 链接{index + 1}抽奖失败: {response.text}")
            else:
                description = response.json()['prizeName']
                self.logger.info(f"链接{index + 1}抽奖获得{description}\n")
            time.sleep(5)
    def info(self):
        url = "https://cloud.189.cn/api/portal/getUserSizeInfo.action"
        headers = {
            "Accept": "application/json;charset=UTF-8"
        }
        response = self.session.get(url,headers=headers)
        res_json = response.json()
        if res_json.get("res_code") == 0:
            self.free_size = res_json['cloudCapacityInfo']['freeSize'] // (1024*1024*1024)
            self.total_size = res_json['cloudCapacityInfo']['totalSize']// (1024*1024*1024)
            self.used_size = res_json['cloudCapacityInfo']['usedSize'] // (1024*1024*1024)
            #print(res_json)
        else:
            self.logger.error(f"❌ 获取用户信息失败: {response.text}")
if __name__ == '__main__':
    app_name = "天翼云盘签到"
    app_env_name = "TYYP_PHONE TYYP_PASSWORD"
    local_script_name = os.path.basename(__file__)
    local_version = '2025.01.12'
    run = Run(app_name=app_name,
              app_env_name=app_env_name,
              script_version=local_version)
    run.main()
