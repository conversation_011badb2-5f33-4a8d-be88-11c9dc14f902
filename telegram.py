# !/usr/bin/python3
# -- coding: utf-8 --
# -------------------------------
# Telegram 签到
# <AUTHOR>
# @Time 2024.09.15
# @Description
# ✨ 功能：
#     Telegram 签到，用于各种机场，公益服务保号
# ✨ 变量获取：
#     打开 https://docs.telethon.dev/en/stable/basic/signing-in.html ，按照文档获取 api_id, api_hash
# ✨ 设置青龙变量：
#     export TELEGRAM_CREDENTIALS='appid@apihash@AuthString' 参数值，多账号换行分割
# -------------------------------
# cron "18 9 * * *" script-path=xxx.py,tag=匹配cron用
# const $ = new Env('Telegram 签到');
# -------------------------------
from tools.common import BaseRun
#from typing import override
from datetime import datetime
from telethon import TelegramClient
from telethon.sessions import StringSession
import os
import asyncio

class Run(BaseRun):
    #@override
    def init_vars(self):
        self.proxy = ('http', '**********', 7890)
    #@override
    def process_vars(self, info):
        split_info = info.split('@')
        self.api_id = int(split_info[0])
        self.api_hash = split_info[1]
        self.auth_string = split_info[2]
        self.day_of_week = datetime.now().weekday()
    #@override
    async def process(self, info, _):
        self.logger.info("===> 变量准备")
        self.process_vars(info)
        # 初始化客户端
        self.logger.info("===> 初始化 Client")
        await self.initialize_client()
        self.logger.info("===> 开始打卡")
        tasks  = (self.handle_getfree_cloud(),
                  self.handle_ikuuu_vpn(),
                  self.handle_erciyuan_emby(),
                  self.handle_feiyuexingkong_music())
        await asyncio.gather(*tasks)
    async def initialize_client(self):
        self.client = TelegramClient(StringSession(self.auth_string), self.api_id, self.api_hash, proxy=self.proxy)
        await self.client.start()

    async def handle_bot_interaction(self, req_entity, resp_entity,
                                     command, success_message, sleep_time=0):
        try:
            await self.client.send_message(req_entity, command)
            await asyncio.sleep(sleep_time)
            result = await self.client.get_messages(req_entity, wait_time=1, from_user=resp_entity)
            msg = f"{success_message}\n{result[0].message}"
            await self.client.send_read_acknowledge(req_entity)
            self.logger.info(msg)
        except Exception as e:
            self.logger.error(str(e))

    #Getfree
    async def handle_getfree_cloud(self):
        getfree_cloud_chat_entity = await self.client.get_entity('t.me/GetfreeCloud')
        getfree_cloud_bot_entity = await self.client.get_entity('t.me/GetfreeCloud_Bot')
        #每周二，周五，周日 执行升级
        if self.day_of_week in (1, 4, 6):
            return await self.handle_bot_interaction(getfree_cloud_chat_entity, getfree_cloud_bot_entity,
                                                '/upgrade@GetfreeCloud_Bot', '==> GetfreeCloud\n✅ Getfree Upgraded !!')
        else:
            return await self.handle_bot_interaction(getfree_cloud_chat_entity, getfree_cloud_bot_entity,
                                                '/checkin@GetfreeCloud_Bot', '==> GetfreeCloud\n✅ @GetfreeCloud_Bot Checked !!')
    #ikuuu vpn
    async def handle_ikuuu_vpn(self):
        ikuuu_vpn_bot_entity = await self.client.get_entity("t.me/iKuuuu_VPN_bot")
        return await self.handle_bot_interaction(ikuuu_vpn_bot_entity,ikuuu_vpn_bot_entity,
                                            '/checkin', '==> iKuuuu_VPN\n✅ @iKuuuu_VPN_bot Checked !!', sleep_time=5)
    #二次元 Emby
    async def handle_erciyuan_emby(self):
        erciyuan_emby_bot_entity = await self.client.get_entity("t.me/nijigen_help_bot")
        return await self.handle_bot_interaction(erciyuan_emby_bot_entity,erciyuan_emby_bot_entity,
                                            '/checkin', '==> 二次元 Emby\n✅ @nijigen_help_bot Checked !!', sleep_time=5)
    #飞跃星空
    async def handle_feiyuexingkong_music(self):
        if self.day_of_week == 0:
            #feiyuexingkong_music_chat_entity = await self.client.get_entity(-1002197507537)
            feiyuexingkong_music_bot_entity = await self.client.get_entity("t.me/xingkongmusic_bot")
            messages = self.client.iter_messages(feiyuexingkong_music_bot_entity, wait_time=1, from_user=feiyuexingkong_music_bot_entity)
            async for x in messages:
                if x.buttons:
                    click_button = x.buttons[0][2]
                    await click_button.click()
                    #签到
                    result = await self.client.get_messages(feiyuexingkong_music_bot_entity, wait_time=1, from_user=feiyuexingkong_music_bot_entity)
                    msg = f"==> 飞跃星空音乐服\n✅ 飞跃星空音乐服群组签到成功 !!\n{result[0].message}"
                    self.logger.info(msg)
                    #await self.client.send_read_acknowledge(req_entity)
                    break
        else:
            self.logger.info("==> 飞跃星空音乐服\nℹ️  今天不是周一，跳过飞跃星空音乐服群组签到")
if __name__ == "__main__":
    app_name = "Telegram 签到"
    app_env_name = "TELEGRAM_CREDENTIALS"
    local_script_name = os.path.basename(__file__)
    local_version = '2025.01.12'
    run = Run(app_name=app_name,
              app_env_name=app_env_name,
              script_version=local_version)
    run_result = asyncio.run(run.async_main())

