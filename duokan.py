# !/usr/bin/python3
# -- coding: utf-8 --
# -------------------------------
# 多看阅读 APP
# <AUTHOR>
# @Time 2024.11.19
# @Description
# ✨ 功能：
#     多看阅读APP 签到 基础任务 看广告 下载APP...
# ✨ 抓包步骤：
#     打开抓包工具
#     打开多看阅读APP, 进入 我的--签到 页面
#     找 https://www.duokan.com/growth/user/task/list 请求头中的 Cookie，复制粘贴到变量 DUOKAN_COOKIES
# ✨ 变量示例：
#     export DUOKAN_COOKIES='mi_version=V13.0.8.0.SKJCNXM; _m=1; platform=android; app_id=DuoKan; xxx'，多账号换行分割
# -------------------------------
# cron "33 33 9 * * *" script-path=xxx.py,tag=匹配cron用
# const $ = new Env('多看阅读 APP');
# -------------------------------
from tools.common import BaseRun
#from typing import override
import requests
import os
import time
import json
import hashlib
class Run(BaseRun):
    #@override
    def init_vars(self):
        self.session = requests.Session()
        self.session.verify = False
        self.headers = {
            "Content-Type": "application/x-www-form-urlencoded; charset=utf-8"
        }
        self.gift_code_list = [
            "d16ad58199c69518a4afd87b5cf0fe67",
            "828672d6bc39ccd25e1f6ad34e00b86c",
            "f0ccc1bb1cecea673c197b928fb8dbd9",
            "6b86c490d92a138de9a0ae6847781caa",
            "c707047e8b820ba441d29cf87dff341e",
            "82b2c012a956b18cff2388d24f2574a6",
            "87d6b5183a361ee1f6ea8cece1ee83c3",
            "9d42576f7e99c94bb752fde06e6770a5",
            "e58d1f67a82a539d9331baaa3785a943",
            "52c95192ebcb1d0113a748df58a72055",
            "511f33e481fe4504d2637aaf6cbbbaff",
            "6e986f36f4a45cadf61d2f246b27cdc6",
            "f27797a6a1d7fe495b0f4de05f799327",
            "4bd335e899fa665f15eea2f215156321",
            "9355df762183f084473432b5c6900c44",
            "4fb21fb04cbbae9d65556c3958603674",
            "2d02ceb4f1bc916510c7407ce4eca5a5",
            "ef314bf665af0b51294e624244acd7d6",
            "1b441a2ab8e9e7dcf11a55b85931132f",
            "005d2345782ab456e5af167336b70623",
            "51ac508a4d494654035f17f1d646779b",
            "0f6579670f1081f1bcba89dd64645b48",
            "0cd858abe26f0d3db561185fe26bbb75",
            "b5f5fd5b47fd587cb003807e97bed783",
            "6ac9509a5cb799efeb1bb877c505f7e3",
            "b5dd986ffc84762429901ffe633d82a0",
            "f98a436cc2c85943d23d986a8d84c3bd",
            "6fc387f2a17b8564ca212e2b16544cc3",
            "12ead6a62411402378c6311199a0b2ef",
            "7d8dcf31e2e69fcf6bd8af4f48831e92",
            "446c3d0303b0dbd6bc2157844f1222ad",
            "439890227d823ff57bed8ad351fa1b75",
            "645acf3107722ab26b9d3194ecd156ff",
            "afcb41dd9bc54d752c26ace985b49960",
            "1100ab94ccd2e8373af70326c194d8ea",
            "373d73c0c0975cf959eb4c40dc82b27c",
            "2167ac28833149e9ad4ca217bcfa1a62",
            "80547afccc42f34e4c8c4083e00a41a6",
            "b604dda473644bd8157bafdf4ae518dc",
            "15eaa8f727b595d512b82f55364b53b9",
            "8fb656937fd613ccbbcacdc384595b03",
            "dd8410da0b5144ba4aba5a618723b72e",
            "204208386b056a2288e541110bfeeec3",
            "c5b2e7344efd4128bcab5840fa427103",
            "0168601e4335095c502e2e550ca53114",
            "dfa12fe056a8deee35da18613173560f",
            "ed945efdef9c7b2de41249a4fed3945e",
            "b9ece5964ab62d51f8b70ffd35191e9d",
            "f0e0ca4ca0b8afd766821a4922a2873c",
            "5c687b8c6bd641f3f2c0d6aaeceafff6",
            "c983be6420027231d77b748f9d02c1f2",
            "7c53358df8156d979cb6cbb74e15877b",
            "a58058035f73628a7c0847c66c350e88",
            "79dd039ca5cf401993801710f9900d6b",
            "5aff116c2cec01fcc69b389034f456a2",
            "d006927cd9bfd620a6af4f76ee3c4100",
            "410fe62830eeb91ca48be24ffe596364",
            "9d18226ff144a72812d0104ce59fb34e",
            "de439c7f75ca80b1d5b8aba619ee200d",
            "00d1a0479590793294bfdd5c427643aa",
            "d57176b1ce88135243bd501e448b8559",
            "7c500eff681637b97dd526bb11737abb",
            "3e197e47aaac926ccd50c37eb2828311",
            "7db084ea5987f841ad77240bcbb8ce54",
            "cce74f0facc50d47c0dd0e3e2f7435fb",
            "f8bb53fbeb9b2d45db8aca1401817599",
            "5baf7f0f355db11eeb0e936b675cdb82",
            "4478a3354de6bcd7e91b49e28a2b2b3f",
            "66a0338d93af82e956122288b08d2b4b",
            "9f598b2b1c9cd0f2b20e335831cce366",
            "9f4a45fec88b2820653abba179759eb6",
            "41086649c9a39ec977ba42f9ce81f828",
            "06ccca6fd73a6e38f65638ab8abbab76",
            "0cfa0a034a203bb3a22be499e74906f4",
            "c0d1da35a8878b7e4dcdf44bf3cd6b96",
            "f34921e16f6518c1149cc083bd8e1ad7",
            "ed0be3c70075d1d8f1a412f9e59a12e7",
            "eb4d6324bae7db952bd220cb4d57a3de",
            "5ba65d9f8ad735681b594f5092f6ab37",
            "2fa6e0b612962937edb37ed7043923fd",
            "baa8268c7d85d793011c5f5b977f8d4b",
            "f4842a465e4583646abf7df67d8e2915",
            "12c6332c8c9ded3d58d45f2dae7de8da",
            "f56609232205692acf6b6a5d337b0965",
            "3e4eed15387843c668fba53641599d07",
            "d1b9d9ede145b5d426130986245cb66e",
            "2979e43f6ab786f5d68cc262105f3c45",
            "118a18ed578c78f4855b416f8271b29a",
            "9122e158d034f094627c70ed6c3d0c33",
            "dd5413c17253e86cc4247984f3bb77e5",
            "b36bb0124b962efccbb601486665ce9e",
            "6afb3a719f8b0a0b2f744b3dad8b15ab",
            "faf18d64268402ed2975a3f60bc9e651",
            "9f4081944d4ca3fa7b831d7c3b6c289d",
            "367d7a3d77a9f96cbd7903b33c30b61f",
            "605276cf621ff9ba34a99e3675a006f6",
            "a50a734c1a3a749918e20205505ef91d",
            "271ff14ba5edfe89a80a3430227bc11b",
            "3bae338062b4bb3a5087eb13cbcc6efe",
            "9b443d60178a9bcb08bae62c41970abf",
            "a4f6e97741054f3567ab6a7257c63ab1",
            "e06a82cc1f05eda4947e5fa0927d89c5",
            "4fa3b4fc274c283efb02c0a1ddd133e7",
            "4aa59e16a3961ed1ebd12b7f15d79547",
            "f75fe88eaa24fc28ac57d963d8b90f2d",
            "42cbe52b6f74761a5a7a79bf370c30ef",
            "7d4571b5c9710e3b5481330bc7123ecc",
            "fcf2f7ec42086809991de5aed5e7ef0d",
            "bb7de9aaf68a83ac1ddbe75ba913b8af",
            "a9bd964b97e785fffb641edb9b402d3f",
            "6a815be6f537b2351e947ed66f74e209",
            "27ae4e4d71395c6255bf7ea57c496507",
            "2b07f369e90f4fc34ef419d891a2906f",
            "7a2dc8a5b3fc0c7ecddb97ed1ce2c833",
            "e7ad152ef27beb80c5d343f41f885b21",
            "ba21758aed15a3a20a27f63bc0d84626",
            "3820f7b8e1ece2614a11264501b5c93e",
            "c3c41c87e6bf752f5237b4fffa33f08b",
            "ed21086ff6682ab8495ecbfbb697af4a",
            "5a2585ff3524f319dfd1f6b735c9a18d",
            "0e61444507f0a780a1c83b612eb5fb9b",
            "b105aa5c696648c0f7aae9e3933f8fe0",
            "fec8f729e9e1d02248b949ce17674e0c",
            "d3323d5560d15d4bc03575dcd0f53ae9",
            "15fbf9d24dd05d9d64a18a8fd28f4dcc",
            "ac0f3bda53081eee547882b2cdc8b04f",
            "5dd3fadcd4ea6b922e1462431966c2bf",
            "4acb71816dad0ce9a53d8fee301d857c",
            "4c7e173f3a046919587db5b2640896e7",
            "8407dc0459d0b367eaced7e5dfdef8ed",
            "17e02409659223ff4e32cabd9ad352d9",
            "c49edc07086b27769eddb981359f56b2",
            "344822f5d8d53fe9aa7a1c7328cd2c59",
            "92259343c65ac0feab5cb56b2e851783",
            "e1e537b0bd37091c0ba4d5f614af9160",
            "dff1116c175ddaaa20f3985a3d88abc6",
            "3b1131a7c7273aa61cbd71b044e9beca",
            "431aab37ef168c383f078b9244008cee",
            "96c3bb8355d7e3ed7265095374f1c090",
            "c3a7d304cdb307f073bef5003d1b8b78",
            "627d884fc905cc353d0028076e39846b",
            "36ce0d88a6bb2d10e0dc0a697f64df4e",
            "dc8dbd035d42a5d8170976d5f532dab0",
            "01c2665e7ea15bc56cca6d955c2e8ae1",
            "c54ae7eeedc87ac52249684f012d3805",
            "2df9b3b8f21a682b20d9d77669087a7a",
            "fded473150a783586c12692fd57d0825",
            "580499e69f42c0ccba0d1f87a83e41e9",
            "99433cb83f1cd7176b7cdeaa7be49cd8",
            "fbd76e8265547376905b3b6004150064",
            "362768496052ae0dfbe909a9b5c6f54e",
            "4f33581089c90944e5ad950646b17712",
            "bffe93cdfe4b8833190e0a59c779e027",
            "78e042b792c3af7faf7a6ebfedf6af9e",
            "51a59c881726c2887efe9752bd9db715",
            "a46ecf03d3f4038ba3de4ae4ac28170f",
            "48d025f7cc34ac29c21d03b2c1f36449",
            "8c9ceb77d61c20cb96ee652eb7b838c9",
            "47a5882c89671429ae532339b7f333ce",
            "a0b735557416ff3d08d3d8440393061a",
            "976d3b3a8fbdf33d525075a9288455ab",
            "636ca4c1db1c4450431ecd7e10a5e671",
            "8c5cd12180027ee6535a837bd4f0259a",
            "b82315333974c76793b3c7f517fe977c",
            "6143d1f3472cd7cf08e3780918019158",
            "20d032426fd66d49bec4f99579252cfa",
            "398ee715d1dfd058a912bc7768d35f82",
            "1f678678966444fb53d118b8134ceb94",
            "d6641f3ed9444eae2b77ba68d3552f6a",
            "ab2babaa19539895a5285c1ded6de8c6",
            "5bc61d3cd53582b859db9cf04fc7e250",
            "5deb619ed27c2754df4f9c7e3ce16b82",
            "b81a322830fee59c75985626f7e0a8b5",
            "e2313ad53d58e181c5fbaef29e5772c3",
            "70d2aa99ef48b6cf1c0e8c107c0e121d",
            "0633cdb06253a2b11e9a9ca234a3e9c3",
            "bd1cbb9764fba94e8f1c0d1c024487af",
            "301cbdbf26210596f9b22123abff0ca8",
            "1fc2448ee192a1d0806ae1eb6fcc81fe",
            "306247030d0b6442c3ded42e9ca99872",
            "1c8f9a0786a01db1d06989345887967e",
            "256ec3a54aaae719aae88d8f9c7f9b5f",
            "45645896cccec48191916fec482979d9",
            "c3a19c728d6fd39925bd63abe15aa446",
            "15f45c4cd8fd4a6c0a3fae14ccafff47",
            "a082c46b09772739af41f01676e1d0d1",
            "14928418f94f5d35b182001ae0160455",
            "dfbc5bc946c72650adaaf570f11a1e80",
            "8a312e3e30d2e8fd1cf8873c3abe1d8c",
            "ef425403acaabfb2a5b3f6ab0aafce8c",
            "c78d471822dd961a53afe23e6c2dfa61",
            "a40f670d8de3784b54784daf63095d88",
            "49a72ace7fd54d8d0833bb2590db58aa",
            "38e3808d28de73af3578f6d64020e1fc",
            "a8be6ab39263d2edf61acafc60949921",
            "d9c16bf0032800916e948ea26624a253",
            "dbf3a62ff403c3ba94d5ab1e6219f5bc",
            "3a6415de684e2978ce17543d66d523f6",
            "2f69a681ee1ff927df1bdbd5431ced1d",
            "e55c0390872735ec285dad8ebdd939e0",
            "1e8f6296f80838df90cfc7a7cc540e65"
        ]
        self.code_list = [
            "K7S36GFSZC",
            "A2AMBFHP6C",
            #"K5HHKUU14D",
            "J18UK6YYAY",
            "1BJGW140U5",
        ]
        self.base_url = "https://www.duokan.com"
    #@override
    def process_vars(self, info):
        self.session.headers.update(self.headers)
        self.cookie = info
        self.cookie_dict = self.tools.get_cookie_from_str(info)
        self.session.headers.update({'Cookie': self.cookie})
    #@override
    def process(self, info, _):
        self.process_vars(info)
        self.logger.info("===> 签到")
        self.sign()
        self.logger.info("===> APP 下载任务")
        self.download()
        self.logger.info("===> APP 试玩任务")
        self.gift()
        self.logger.info("===> 其他任务（广告视频等）")
        self.task()
        self.logger.info("===> 抽奖")
        self.draw()
        self.logger.info("===> 每日阅读")
        self.read()
        self.logger.info("===> 每日阅读免费小说")
        self.free_read()
        msg = self.info()
        self.logger.final(msg)
    def get_data(self):
        device_id = self.cookie_dict.get("device_id")
        t = int(time.time())
        t_device_id = f"{device_id}&{t}"
        c = 0
        for one in t_device_id:
            c = (c * 131 + ord(one)) % 65536
        return f"_t={t}&_c={c}"
    def get_sign_data(self):
        device_id = self.cookie_dict.get("device_id")
        t = int(time.time())
        t_device_id = f"{device_id}&{t}"
        c = 0
        for one in t_device_id:
            c = (c * 131 + ord(one)) % 65536
        return {"_t": t, "_c": c}
    def sign(self):
        """
        签到部分
        """
        sign_data = self.get_data()
        #1签到
        url = f"{self.base_url}/api/dk-user/checkin"
        sign_res = self.session.post(url=url, data=sign_data).json()
        sign_coins = 0
        if sign_res.get("msg") == "成功":
            sign_coins = sign_res.get("data").get("gift").get("value")
            self.logger.info(f"✅ 今日签到成功，得到书豆 {sign_coins}")

        #2签到成功看广告视频
        sign_video_coins = 0
        sign_video_coins_per = 3
        url = f"{self.base_url}/events/video_gift"
        gift_code = 'EBTXJHUJS6'
        iter = 1
        while True:
            sign_video_data = f"code={gift_code}&chances=9"
            sign_video_res = self.session.post(url=url, data=sign_video_data).json()
            if sign_video_res.get("result") != 0:
                #self.logger.error(f"❌ 获取奖励失败，原因：{sign_video_res.get('msg')}")
                break
            #延迟5s
            time.sleep(5)
            self.logger.info(f"✅ 看第 {iter} 个签到广告视频奖励获得书豆 {sign_video_coins_per}")
            sign_video_coins += sign_video_coins_per
        self.logger.info(f"🎁 签到部分共得到书豆：{sign_video_coins + sign_coins}")
    def download(self):
        """
        APP 下载任务
        """
        url = f"{self.base_url}/events/common_task_gift"
        success_count = 0
        coin = 30
        download_coins = 0
        chance_code = 'J18UK6YYAY'
        chances = (17, 0)
        error_count = 0
        while True:
            for chance in chances:
                data = f"code={chance_code}&chances={chance}"
                res = self.session.post(url=url, data=data).json()
                if res.get("result") == 0:
                    #print(f"下载APP任务，获得书豆{coin}")
                    download_coins += coin
                    success_count += 1
                else:
                    error_count += 1
                time.sleep(5)
            if error_count == 2:
                break
        self.logger.info(f"🎁 完成任务 {success_count} 个, 共获得书豆：{download_coins}")
    def gift(self):
        """
        APP 试玩任务
        """
        success_count = 0
        url = f"{self.base_url}/events/common_task_gift_check"
        chances_codes = [
            "KYKJF7LL0G",
            "QNZX91D1CK"
        ]
        data = f"code={chances_codes[0]}&{self.get_data()}&withid=1"
        res = self.session.post(url=url, data=data).json()
        if res.get("chances") == 0:
            self.logger.info("❌ 体验任务: 已经做完啦")
            return
        else:
            app_coins = 0
            url = f"{self.base_url}/events/common_task_gift"
            for gift_code in self.gift_code_list:
                for code in chances_codes:
                    while True:
                        data = f"code={code}&chances=1&sign={gift_code}&{self.get_data()}&withid=1"
                        res = self.session.post(url=url, data=data).json()
                        if res.get("msg") == "成功":
                            self.logger.info("✅ 完成体验任务，获得书豆 30")
                            app_coins += 30
                            success_count += 1
                        else:
                            break
                        time.sleep(1.5)
        self.logger.info(f"APP 体验试玩任务: 完成 {success_count} 个，共获得书豆：{app_coins}")
    def task(self):
        """
        其他任务（广告视频等）
        """
        success_count = 0
        success_coins = 0
        url = f"{self.base_url}/events/tasks_gift"
        #旧任务
        for code in self.code_list:
            while True:
                data = f"code={code}&chances=3&{self.get_data()}&withid=1"
                res = self.session.post(url=url, data=data).json()
                if res.get("result") != 0:
                    #self.logger.info("❌ " +  res.get("msg"))
                    break
                success_count += 1
        #看小视频赚书豆
        video_check_coins = 3
        video_check_coins_count = 0
        video_check_count = 0
        chances_code = "A2AMBFHP6C"
        while True:
            data = f"code={chances_code}&chances=10&{self.get_data()}"
            res = self.session.post(url=url, data=data).json()
            if res.get("result") != 0:
                #self.logger.info("❌ " +  res.get("msg"))
                break
            self.logger.info(f"✅ 看小视频任务获得书豆 {video_check_coins}")
            video_check_coins_count += video_check_coins
            video_check_count += 1
        res_msg = f"🎁 完成任务 {success_count + video_check_count} 个，共获得书豆：{success_coins + video_check_coins_count}"
        self.logger.info(res_msg)
    def read(self):
        try:
            while True:
                url = f"{self.base_url}/sync/progress/upload"
                payload = {
                    'book_id': "55bf74e5180b4a189e7caf23299b5196",
                    'book_name': "参与感：小米口碑营销内部手册（珍藏版）.EPUB",
                    'duokan': "1",
                    'percent': "4500",
                    'time': "30",
                    'word_count': "-1637",
                    'data': "{\"SpecVersion\":\"2.0\",\"DeviceID\":\"D006300c65b1b4bee4ab7d934b18e0cbebbd763\",\"KernelVersion\":\"5.0.0.d8a2110\",\"BookID\":\"55bf74e5180b4a189e7caf23299b5196\",\"BookRevision\":\"20180711.1\",\"Item\":{\"Type\":\"PROGRESS\",\"DataID\":\"0\",\"CreateTime\":\"1736446666\",\"LastModifyTime\":\"1736446666\",\"RefPos\":[\"afbc4793-fc91-43e4-97e3-8c20dd5fa5de\",2364,76,5,192]}}",
                }
                payload.update(self.get_sign_data())
                response = self.session.post(url, data=payload)
                read_progress = response.json()
                if read_progress.get('result') == 0:
                    url = f"{self.base_url}/task/v2/user/list"
                    payload = self.get_data()
                    response = self.session.post(url, data=payload)
                    user_list_data = response.json().get('data')
                    for day_read_task in user_list_data:
                        if day_read_task.get('name') == "百读不厌":
                            if day_read_task.get('status') == 2:
                                done_value = day_read_task.get('done_value')
                                value = day_read_task.get('value')
                                if done_value < value:
                                    #self.logger.info(f"当前进度：{done_value*100 // value}%")
                                    time.sleep(3)
                            elif day_read_task.get('status') == 1:
                                self.logger.info("ℹ️  阅读任务未开始")
                                return
                            else:
                                url = f"{self.base_url}/task/v2/user/claim"
                                payload = {"task_id": day_read_task.get('task_id')}
                                payload.update(self.get_sign_data())
                                res = self.session.post(url, data=payload).json()
                                self.logger.info(f"✅ 阅读任务已完成, 获得 {res.get('data').get('coins')}")
                                return
                        else:
                            return
                else:
                    self.logger.info("❌ 阅读任务失败")
                    break
        except Exception as e:
            self.logger.info(f"❌ 阅读任务失败{e}")
    def free_read(self):
        try:
            #查询可免费阅读书籍
            url = f"{self.base_url}/hs/v4/channel/query/588"
            params = {'user_type': "3",'fetch_pos': "head"}
            payload = {'features': "{}",'withid': "1"}
            payload.update(self.get_sign_data())
            response = self.session.post(url, params=params, data=payload)
            free_book_data = response.json()
            book_title = free_book_data['items'][1]['data']['data'][0]['title']
            book_id = free_book_data['items'][1]['data']['data'][0]['fiction_id']
            coins_sum = 0
            #阅读 100 次
            for i in range(1,100):
                #间隔 1.5s
                time.sleep(1.5)
                url = f"{self.base_url}/sync/progress/upload"
                data = {
                    "SpecVersion": "2.0",
                    "DeviceID": self.cookie_dict.get("device_id"),
                    "KernelVersion": "5.0.0.d8a2110",
                    "BookID": book_id,
                    "BookRevision": "",
                    "Item": {"Type": "PROGRESS","DataID": "0","CreateTime": "1737046824","LastModifyTime": "1737046824","RefPos": ["0", 0, 0, -1, 0],},
                }

                data_str = json.dumps(data,separators=[",", ":"])
                payload = {
                    'book_id': book_id,
                    'book_name': f"{book_title}.EPUB",
                    'duokan': "1",
                    'percent': "0",
                    'time': "610",
                    'word_count': "-546",
                    'data': data_str,
                }
                payload.update(self.get_sign_data())
                response = self.session.post(url, data=payload)
                res = response.json()
                if res.get('result') != 0:
                    self.logger.info(f"❌ 第 {i} 次读免费小说任务失败")
                    break
                else:
                    coins_sum += self.free_read_receive_award(i)
            self.logger.info(f"✅ 读免费小说任务已完成, 获得书豆：{coins_sum}")
        except Exception as e:
            self.logger.info(f"❌ 读免费小说任务失败{e}")
            self.logger.info(f"⚠️  读免费小说任务部分完成, 获得书豆：{coins_sum}")
    def free_read_wait_click_receive(self):
                #领取奖励
        wait_list_url = f"{self.base_url}/growth/user/task/list"
        wait_list_payload = {
            'activity_id': "1138",
            'all_required': "1",
        }
        wait_list_payload.update(self.get_sign_data())
        #查询当前待领取的奖励列表
        data = self.session.post(wait_list_url, data=wait_list_payload).json()
        wait_click_receive = [i for i in data['data'] if i.get('stairs')[0].get('button_status') <= 3]
        return wait_click_receive
    def free_read_receive_award(self, i):
        wait_click_receive = self.free_read_wait_click_receive()
        stair_id = "1000"
        receive_url = f"{self.base_url}/growth/user/task/claim"
        key = "CWoAh5nU1pjDGUGpcIxRbfpva1iPK0rb"
        uid = self.cookie_dict.get("user_id")
        if len(wait_click_receive) > 0:
            #stair_id=1000&task_id=2048&withid=1&uid=184956742&key=CWoAh5nU1pjDGUGpcIxRbfpva1iPK0rb
            task_id = wait_click_receive[0].get('task_id')
            sign_md5 = hashlib.md5(f"stair_id={stair_id}&task_id={task_id}&withid=1&uid={uid}&key={key}".encode()).hexdigest()
            payload = {
                'task_id': task_id,
                'stair_id': "1000",
                'sign': sign_md5,
            }
            payload.update(self.get_sign_data())
            resp = self.session.post(url=receive_url, data=payload)
            resp_data = resp.json()
            if resp_data.get('result') == 0:
                return resp_data.get('data')[0].get('value')
            else:
                self.logger.info(f"❌ 第 {i} 次读免费小说任务失败")
                return 0
        return 0
    def delay(self, date):
        '''
        书豆延期
        '''
        url = f"{self.base_url}/store/v0/award/coin/delay"
        data = f"date={date}&{self.get_data()}&withid=1"
        return self.session.post(url=url, data=data).json()
    def info(self):
        '''
        获取用户当前的书豆信息，并尝试将即将到期的书豆进行延期。

        该方法首先构造请求以获取用户的书豆数据，然后根据返回的数据计算当前书豆总数。
        如果存在可以延期的书豆，将调用delay方法进行延期操作，并记录操作结果。
        如果账号Cookie失效，将记录账号异常信息。
        '''
        # 构造获取书豆信息的URL和数据
        url = f"{self.base_url}/store/v0/award/coin/list"
        data = f"sandbox=0&{self.get_data()}&withid=1"

        # 发送请求并解析响应数据
        res = self.session.post(url=url, data=data).json()

        # 检查响应消息，判断是否登录
        if "尚未登录" not in res.get("msg"):
            # 计算当前书豆总数
            coin = sum(one.get("coin") for one in res.get("data", {}).get("award"))
            msg = f"当前书豆：{coin}\n"
            # 遍历每个书豆奖励，检查是否有可延期的书豆
            for one in res.get("data", {}).get("award"):
                if one.get("delay") == 1:  # 判断是否有可延迟的豆子
                    # 调用delay方法尝试延期书豆，并记录结果
                    res = self.delay(one.get("expire"))
                    msg += f"{one.get('expire')} 到期，{one.get('coin')} 书豆 | 延期：{res.get('msg')}\n"
                else:
                    msg += f"{one.get('expire')} 到期，{one.get('coin')} 书豆\n"
        else:
            # 如果检测到Cookie失效，记录账号异常信息
            msg = "❌ 账号异常: Cookie 失效"
            # 记录书豆信息或异常信息
            #self.logger.error(msg.strip)
        #去除末尾换行
        msg = msg.strip()
        #self.logger.info(msg)
        return msg
    def draw(self):
        """
        执行抽奖事件的方法。

        添加抽奖次数。
        通过POST请求向服务器请求添加抽奖机会，直到请求不再成功。
        成功添加的抽奖次数会被记录并打印出来。

        通过向特定URL发送请求来执行抽奖操作，记录成功抽奖的次数，直到抽奖不再成功为止。
        """
        success_count = 0
        url = f"{self.base_url}/store/v0/event/chances/add"
        chances_code = "8ulcky4bknbe_f"
        while True:
            data = (f"code={chances_code}&count=1&{self.get_data()}&withid=1")
            res = self.session.post(url=url, data=data).json()
            if res.get("result") == 0:
                success_count += 1
            else:
                #self.logger.info(f"❌ {res.get('msg')}")
                break
        self.logger.info(f"✅ 添加抽奖: {success_count} 次")

        success_count = 0
        draw_coins = 0
        url = f"{self.base_url}/store/v0/event/drawing"
        chances_code = "8ulcky4bknbe_f"
        while True:
            data = f"code={chances_code}&{self.get_data()}&withid=1"
            res = self.session.post(url=url, data=data).json()
            if res.get("result") == 0:
                success_count += 1
                if len(res['award']) > 0:
                    draw_coins += res['award'][0]['total']
                    self.logger.info(f"✅ 抽奖获得书豆: {res['award'][0]['total']}")
            else:
                #self.logger.info(f"❌ {res.get('msg')}")
                break
        self.logger.info(f"🎁 成功抽奖 {success_count} 次，共获得书豆：{draw_coins} ")
    @DeprecationWarning
    def free(self, cookies):
        url = f"{self.base_url}/hs/v4/channel/query/403"
        res = self.session.get(url=url, cookies=cookies, headers=self.headers).json()
        bid = res.get("items")[0].get("data").get("book_id")
        data = f"payment_name=BC&ch=VSZUVB&book_id={bid}&price=0&allow_discount=1"
        free_url = f"{self.base_url}/store/v0/payment/book/create"
        res = self.session.post(url=free_url, data=data).json()
        if "尚未登录" in res.get("msg"):
            return "今日限免: Cookie 失效"
        book_title = res.get("book").get("title")
        book_msg = res.get("msg")
        return f"今日限免: {book_title} · {book_msg}"
if __name__ == "__main__":
    app_name = "多看阅读"
    app_env_name = "DUOKAN_COOKIES"
    local_script_name = os.path.basename(__file__)
    local_version = '2025.01.08'
    run = Run(app_name=app_name,
              app_env_name=app_env_name,
              script_version=local_version)
    run.main()



