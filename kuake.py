# !/usr/bin/python3
# -- coding: utf-8 --
# -------------------------------
# 夸克网盘
# <AUTHOR> qianfan<PERSON><PERSON>jin
# @Time : 2024/9/8 16:23
# @Description
# ✨ 功能：
#     夸克网盘签到，签到可获得容量
# ✨ 抓包步骤：
#     打开夸克 APP，进入网盘页面，找到签到获取容量
#     执行一次签到:
#       找 https://drive-m.quark.cn/1/clouddrive/capacity/growth/sign  从请求参数中得到 kps，sign, vcode
# ✨ 变量示例
#     export KUAKE_CREDENTIALS='kps=AAQxxx;sign=AAxx;vcode=17xxx'，多账号换行分割
# -------------------------------
# cron "1 0 10 * * *" script-path=xxx.py,tag=匹配cron用
# const $ = new Env('夸克网盘')
# -------------------------------
from tools.common import BaseRun
##from typing import override
import requests
import os

class Run(BaseRun):
    ##@override
    def init_vars(self):
        self.session = requests.Session()
        self.session.verify = False
        self.base_url = "https://drive-m.quark.cn"
        self.total_capacity = 0
        self.used_capacity = 0
        self.sign_reward = 0
        #签到相关
        self.cur_total_sign_day = 0
        self.sign_rewards = 0
        self.sign_progress = 0
        self.sign_daily_reward = 0
    def process_vars(self, info):
        #self.cookie = info
        self.kps_key, self.sign_key, self.vcode = info.split(';')
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3',
            "content-type": "application/json",
            # "cookie": self.cookie,
        }
        self.querystring = {
            "kps": self.kps_key,
            "sign": self.sign_key,
            "vcode": self.vcode,
            "pr": "ucpro",
            "fr": "android",
        }
        self.session.headers.update(headers)

    def sign(self):
        url = f"{self.base_url}/1/clouddrive/capacity/growth/sign"
        payload = {"sign_cyclic": True}
        response_json = self.session.post(url, json=payload, params=self.querystring).json()
        #res_data = response_json.get("data",'')
        try:
            if not response_json.get("message"):
                self.logger.info(f"✅ 今日签到成功，获得 {response_json['data']['sign_daily_reward'] // (1024*1024)} MB")
            elif response_json.get("message") == "cap_growth_sign_repeat":
                self.logger.info("ℹ️  今日已签到")
            else:
                self.logger.error(response_json)
                return False
        except Exception:
            pass

    def info(self):
        url = f"{self.base_url}/1/clouddrive/capacity/growth/info"
        response_json = self.session.get(url, params=self.querystring).json()
        res_data = response_json.get("data",'')
        if not response_json.get("message"):
            #容量相关
            self.total_capacity = res_data.get("total_capacity",'')
            self.used_capacity = res_data.get("use_capacity",'')
            self.sign_reward = res_data['cap_composition']['sign_reward']

            #签到相关
            self.cur_total_sign_day =  res_data['cap_growth']['cur_total_sign_day']
            self.sign_rewards = res_data['cap_sign']['sign_rewards']
            self.sign_progress = res_data['cap_sign']['sign_progress']
            self.sign_daily_reward = res_data['cap_sign']['sign_daily_reward']
        else:
            self.logger.error(response_json)
            return False
    def show_info(self):
        msg = ""
        msg = f"总共空间: {self.total_capacity//(1024*1024*1024)} GB\n已用空间: {self.used_capacity//(1024*1024*1024)} GB\n"
        msg += f"今日签到获取: {self.sign_daily_reward // (1024*1024)} MB\n"
        msg += f"累计签到获取: {self.sign_reward // (1024*1024)} MB\n"
        msg += f"累计签到天数: {self.cur_total_sign_day} 天，当前周期签到第 {self.sign_progress} 天\n"
        msg += "剩余签到奖励: \n"
        for i, reward in enumerate(self.sign_rewards):
            msg += f"后面 { i+1 }天: {reward['name']}\n"
        return msg.strip()


    #@override
    def process(self, info, _):
        self.process_vars(info)
        self.logger.info("===> 执行签到")
        self.sign()
        self.logger.info("===> 获取空间信息")
        self.info()
        self.logger.final(self.show_info())
if __name__ == "__main__":
    app_name = "夸克网盘"
    app_env_name = "KUAKE_CREDENTIALS"
    local_script_name = os.path.basename(__file__)
    local_version = '2025.01.09'
    run = Run(app_name=app_name,
              app_env_name=app_env_name,
              script_version=local_version)
    run.main()

