# !/usr/bin/python3
# -- coding: utf-8 --
# -------------------------------
# 熊猫代理
# <AUTHOR>
# @Time 2024.11.03
# @Url https://www.xiongmaodaili.com/
# @Description
# ✨ 功能：
#       熊猫代理IP, 签到获取积分，积分可换余额，余额可购买网站代理，也可兑换实物礼品
# ✨ 抓包步骤：
#       无需抓包，只需登录的账号和密码
#       组装为: 账号;密码
# ✨ 变量示例：
#     export XMDL_CREDENTIALS='133xxx;fewfxxxx'，多账号换行分割
# -------------------------------
# cron "33 44 9 * * *" script-path=xxx.py,tag=匹配cron用
# const $ = new Env('熊猫代理');
# -------------------------------
from requests_toolbelt import MultipartEncoder
from tools.common import BaseRun
#from typing import override
import requests
import os

class Run(BaseRun):
    #@override
    def init_vars(self):
        self.session = requests.Session()
        self.session.verify = False
        self.headers = {
            #"Accept": "application/json, text/plain, */*",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36",
        }
        self.base_url = "https://www.xiongmaodaili.com"
        self.before_point = 0
        self.after_point = 0
    #@override
    def process_vars(self, info):
        self.session.headers.update(self.headers)
        self.username, self.password = info.split(";")
    #@override
    def process(self,info,_):
        self.process_vars(info)
        self.logger.info("===> 登录")
        if self.login():
            self.get_personal_info()
            self.logger.info("===> 签到")
            self.sign()
            self.get_personal_info(end=True)
            self.logger.final(f"积分变化：{self.before_point} --> {self.after_point}\n本次签到获得：{self.after_point - self.before_point }")
    #登录
    def login(self):
        #初始化
        self.session.get(f'{self.base_url}')
        url = f"{self.base_url}/xiongmao-web/user/login"
        encoded = MultipartEncoder(
            fields={"account": self.username, "password": self.password, "originType": "1"}
        )
        #encoded = {"account": self.username, "password": self.password, "originType": "1"}
        self.session.headers["Content-Type"] = encoded.content_type
        data = self.session.post(url, data=encoded)
        if data and data.json()["code"] == "0":
            self.logger.info(f"✅ 账号 {self.username} 登录成功！")
            return True
        self.logger.error(f"ℹ️  账号 {self.username} 登录失败：{data}")
        return False
    #签到
    def sign(self):
        #查询签到状态
        self.session.headers["Content-Type"] = "application/json;charset=UTF-8"
        result = self.session.get(f'{self.base_url}/xiongmao-web/points/getSignInDay')
        data = result.json()
        if result.status_code != 200:
            self.logger.error(f"❌ 请求失败，原因：{result.text}")
            return False
        if data["obj"][0]["status"] == 1:
            self.logger.info('ℹ️  已经签到过了')
            return False
        elif data["obj"][0]["status"] == 0:
            pass
        else:
            self.logger.error(f'❌ 查询签到状态失败，原因：{data}')
            return False
        #查询签到天数
        result = self.session.get(f"{self.base_url}/xiongmao-web/points/getSignInDayTime")
        data = result.json()
        if data and data["code"] == "0":
            pass
        else:
            self.logger.error(f"❌ 查询当前签到天数失败：{data}")
            return False
        #签到领取积分
        params = {
            "signInDay": f"{data['obj'] + 1}"
        }
        result = self.session.get(f"{self.base_url}/xiongmao-web/points/receivePoints",params=params)
        data = result.json()
        if data and data["msg"] == "领取成功！":
            self.logger.info("✅ 签到成功")
        else:
            self.logger.error(f"❌ 签到失败：{data}\n")
            return False
        return True

    # 获取个人信息
    def get_personal_info(self, end=False):
        data = self.session.get(f"{self.base_url}/xiongmao-web/points/getUserPoints").json()
        if data and data["code"] == "0":
            #self.logger.info(f"✅ 当前积分：{data['obj']}")
            if not end:
                self.before_point = int(data["obj"])
            else:
                self.after_point = int(data["obj"])
        else:
            self.logger.error(f"❌ 查询当前积分失败：{data}")

if __name__ == "__main__":
    app_name = "熊猫代理"
    app_env_name = "XMDL_CREDENTIALS"
    local_script_name = os.path.basename(__file__)
    local_version = '2024.11.3'
    run = Run(app_name=app_name,
              app_env_name=app_env_name,
              script_version=local_version)
    run.main()
