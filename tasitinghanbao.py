# !/usr/bin/python3
# -- coding: utf-8 --
# -------------------------------
# 塔斯汀小程序
# <AUTHOR>
# @Time 2025.01.11
# @Description
# ✨ 功能：
#     塔斯汀小程序签到获取积分
# ✨ 抓包步骤：
#     打开抓包工具
#     打开塔斯汀小程序
#     授权登陆
#     找到签到请求，获取请求体中的 memberPhone，获取请求头中的 user-token
#     格式：memberPhone;user-token
# ✨ 变量示例：
#     export TASITING_COOKIES='18279xxxx;ssxxx'，多账号换行分割
# -------------------------------
# cron "30 9 * * *" script-path=xxx.py,tag=匹配cron用
# const $ = new Env('塔斯汀小程序')
# -------------------------------

from tools.common import BaseRun
import requests
import json
import os

class Run(BaseRun):
    def init_vars(self):
        self.session = requests.Session()
        self.session.verify = False
        self.base_url = 'https://sss-web.tastientech.com'
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Linux; Android 15; 23013RK75C Build/AQ3A.240912.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/134.0.6998.136 Mobile Safari/537.36 XWEB/1340123 MMWEBSDK/20250201 MMWEBID/8758 MicroMessenger/8.0.58.2841(0x28003A35) WeChat/arm64 Weixin NetType/WIFI Language/zh_CN ABI/arm64 MiniProgramEnv/android',
            'Content-Type': 'application/json',
            'channel': '1',
            'version': '3.21.0',
            'charset': 'utf-8',
            'Referer': 'https://servicewechat.com/wx557473f23153a429/427/page-frame.html'
        }
        # 用户信息
        self.member_phone = ''
        self.user_token = ''
        # 签到结果
        self.sign_result = ''

    def process_vars(self, info):
        cookies = info.split(';')
        if len(cookies) >= 2:
            self.member_phone = cookies[0].strip()
            self.user_token = cookies[1].strip()
            self.headers['user-token'] = self.user_token
            self.session.headers.update(self.headers)
        else:
            self.logger.error('❌ COOKIE格式错误，应为：memberPhone;user-token')

    def process(self, info, _):
        self.logger.info("===>🆕 变量准备")
        self.process_vars(info)
        if not self.member_phone or not self.user_token:
            self.logger.error('❌ 缺少必要的认证信息')
            return

        self.logger.info(f"===>🔛 用户 {self.member_phone}")
        self.logger.info("===>💥 开始签到")
        self.signin()
        self.logger.final(f"签到结果：{self.sign_result}")

    def signin(self):
        url = f"{self.base_url}/api/sign/member/signV2"
        payload = {
            "activityId": 60,
            "memberName": "",
            "memberPhone": self.member_phone
        }

        try:
            response = self.session.post(url, data=json.dumps(payload))
            if response.status_code == 200:
                res_json = response.json()
                code = res_json.get('code', None)
                message = res_json.get('msg', '')

                if code == 200:
                    data = res_json.get('result', {})
                    if data:
                        continuous_num = data.get("continuousNum")
                        point = data.get("point")
                        self.sign_result = f"✅ 签到成功：{message}, 连续签到时间：{continuous_num}，获得积分：{point}"
                        self.logger.info(self.sign_result)
                    else:
                        self.sign_result = f"✅ 签到成功：{message}"
                        self.logger.info(self.sign_result)
                elif code == 500:
                    self.sign_result = f"ℹ️ {message}"
                    self.logger.info(self.sign_result)
                else:
                    self.sign_result = f"❌ 签到失败：{message}"
                    self.logger.error(self.sign_result)
            else:
                self.sign_result = f"❌ 签到请求失败，状态码：{response.status_code}"
                self.logger.error(self.sign_result)
                self.logger.error(f"❌ 响应内容：{response.text}")
        except Exception as e:
            self.sign_result = f"❌ 签到异常：{str(e)}"
            self.logger.error(self.sign_result)

if __name__ == '__main__':
    app_name = "塔斯汀小程序"
    app_env_name = "TASITING_COOKIES"
    local_script_name = os.path.basename(__file__)
    local_version = '2025.01.11'
    run = Run(app_name=app_name,
              app_env_name=app_env_name,
              script_version=local_version)
    run.main()
