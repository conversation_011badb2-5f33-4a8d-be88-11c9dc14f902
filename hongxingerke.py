# !/usr/bin/python3
# -- coding: utf-8 --
# -------------------------------
# 鸿星尔克官方会员中心小程序
# <AUTHOR>
# @Time 2024.09.01
# @Description
# ✨ 功能：
#     鸿星尔克积分签到
# ✨ 抓包步骤：
#     打开鸿星尔克
#     授权登陆
#     打开抓包工具
#     找 hope.demogic.com 请求头中 memberId 以及 enterpriseId
# ✨ 设置青龙变量：
#     export HONGXINGEREK_CREDENTIALS='ff80808xxxxxxxx;ff8080817xxxxxxx' 多账号换行分割
# ✨ 注意
#     抓完CK没事儿别打开小程序，重新打开小程序请重新抓包
# -------------------------------
# cron "4 9 * * *" script-path=xxx.py,tag=匹配cron用
# const $ = new Env('鸿星尔克官方会员中心小程序')
# -------------------------------
from tools.common import BaseRun
#from typing import override
import os
import random
import time
import requests
import hashlib
class Run(BaseRun):
    #@override
    def init_vars(self):
        self.session = requests.Session()
        self.session.verify = False
        self.appid = 'wxa1f1fa3785a47c7d'
        self.base_url = "https://hope.demogic.com/gic-wx-app"
        self.use_power_max = False
        self.before_point = 0
        self.after_point = 0
    #@override
    def process_vars(self, info):
        self.member_id, enterprise_id = info.split(";")
        headers = {
            'Host': 'hope.demogic.com',
            'xweb_xhr': '1',
            'channelEntrance': 'wx_app',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090a13) XWEB/9129',
            'sign': enterprise_id,
            'Accept': '*/*',
            'Sec-Fetch-Site': 'cross-site',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': 'https://servicewechat.com/wxa1f1fa3785a47c7d/55/page-frame.html',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Content-Type': 'application/x-www-form-urlencoded',
        }
        self.defualt_params = {
            'memberId': self.member_id,
            'cliqueId': '-1',
            'cliqueMemberId': '-1',
            'useClique': '0',
            'enterpriseId': enterprise_id,
            'appid': self.appid,
            'gicWxaVersion': '3.9.16'
        }
        self.session.headers.update(headers)
    #@override
    def process(self, info, _):
        self.logger.info("===>🆕 变量准备")
        self.process_vars(info)
        self.get_person_info()
        self.logger.info("===>💥 签到")
        self.sign()
        self.get_person_info(end=True)
        self.logger.final(f"当前用户：{self.phone}\n积分变化：{self.before_point} --> {self.after_point}\n本次签到：{self.after_point-self.before_point }")

    def gen_signature(self):
        secret = 'damogic8888'
        # 获取GMT+8的当前时间戳
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        # 生成随机数
        random_int = random.randint(1000000, 9999999)
        # 构建待加密字符串
        raw_string = f"timestamp={timestamp}transId={self.appid}{timestamp}secret={secret}random={random_int}memberId={self.member_id}"
        # 使用MD5进行加密
        md5_hash = hashlib.md5(raw_string.encode())
        sign = md5_hash.hexdigest()
        self.defualt_params['random'] = random_int
        self.defualt_params['sign'] = sign
        self.defualt_params['timestamp'] = timestamp
        self.defualt_params['transId'] = self.appid+timestamp

    def get_person_info(self,end = False):
        #用户信息
        self.gen_signature()
        url = f"{self.base_url}/get_member_grade_privileg.json"
        self.defualt_params['launchOptions'] = '{"path":"pages/points-mall/member-task/member-task","query":{},"scene":1256,"referrerInfo":{},"apiCategory":"default"}'
        response_user = self.session.post(url, params=self.defualt_params)
        user_data = response_user.json()

        # 获取积分记录
        self.gen_signature()
        url = f"{self.base_url}/integral_record.json"
        self.defualt_params['launchOptions'] = '{"path":"pages/member-center/personal-data/personal-data","query":{},"scene":1106,"referrerInfo":{},"mode":"default","apiCategory":"default"}'
        self.defualt_params['currentPage'] = 1
        self.defualt_params['pageSize'] = 20
        point_response = self.session.post(url, params=self.defualt_params)
        data = point_response.json()
        response_points_info = data.get('response', {})
        accumulat_points = response_points_info.get('accumulatPoints', '')
        if user_data.get('errcode', -1) == 0:
            member = user_data.get('response', {}).get('member',  {})
            if member:
                phoneNumber = member.get('phoneNumber', '')
                self.phone = f"{phoneNumber[:3]}****{phoneNumber[-4:]}"
                if not end:
                    self.defualt_params['wxOpenid'] = member.get('openId', '')
                    self.defualt_params['unionid'] = member.get('thirdUnionid', '')
                    self.before_point = accumulat_points
                else:
                    self.after_point = accumulat_points
            return True
        else:
            self.logger.error(f'❌ 失败：{response_user}')
            return False

    def sign(self):
        self.gen_signature()
        url = f"{self.base_url}/member_sign.json"
        params = self.defualt_params
        params['launchOptions'] = '{"path":"pages/points-mall/member-task/member-task","query":{},"scene":1256,"referrerInfo":{},"apiCategory":"default"}'
        response = self.session.post(url, params=params)
        data = response.json()
        if data.get('errcode', -1) == 0:
            response_data = data.get('response', {})
            member_sign = response_data.get('memberSign', {})
            #今天签到获取
            #integralCount = member_sign.get('integralCount', '')
            #过期积分
            #expire_points = data.get('expirePoints', '')
            #总积分
            #accumulat_points = data.get('points', '')
            continuousCount = member_sign.get('continuousCount', '')
            self.logger.info(f'✅ 签到成功，连续签到：【{continuousCount}】天')
            return True
        elif data.get('errcode', -1) == 900001:
            self.logger.info('ℹ️  今天已经签到过了')
            return False
        else:
            self.logger.error(f'❌ 签到失败： {response.text}')
            return False
if __name__ == '__main__':
    app_name = "鸿星尔克官方会员中心小程序"
    app_env_name = "HONGXINGEREK_CREDENTIALS"
    local_script_name = os.path.basename(__file__)
    local_version = '2025.01.09'
    run = Run(app_name=app_name,
              app_env_name=app_env_name,
              script_version=local_version)
    run.main()
