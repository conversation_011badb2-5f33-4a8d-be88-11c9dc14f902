# !/usr/bin/python3
# -- coding: utf-8 --
# -------------------------------
# 达美乐小游戏
# <AUTHOR>
# @Time 2025.01.18
# @Description
# ✨ 功能：
#       达美乐小游戏玩游戏获取积分抽奖
# ✨ 抓包步骤：
#       1. 达美乐小游戏小程序，玩一把游戏，提交分数抽奖
#       2.
# ✨ 变量示例：
#     export DAMEILE_CREDENTIALS='xxx'，多账号换行分割
# -------------------------------
# cron "19 0 * * *" script-path=xxx.py,tag=匹配cron用
# const $ = new Env('达美乐小游戏');
# -------------------------------
from tools.common import BaseRun
#from typing import override
import requests
import os
class Run(BaseRun):
    #@override
    def init_vars(self):
        self.session = requests.Session()
        self.session.verify = False
        self.headers = {
            'User-Agent': "Mozilla/5.0 (Linux; Android 12; M2012K11AC Build/SKQ1.211006.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/122.0.6261.120 Mobile Safari/537.36 XWEB/1220133 MMWEBSDK/20240404 MMWEBID/8518 MicroMessenger/8.0.49.2600(0x2800313D) WeChat/arm64 Weixin NetType/WIFI Language/zh_CN ABI/arm64 MiniProgramEnv/android",
            'Accept-Encoding': "gzip,compress,br,deflate",
            'Content-Type': "application/x-www-form-urlencoded",
            'charset': "utf-8",
            'Referer': "https://servicewechat.com/wx887bf6ad752ca2f3/63/page-frame.html",
            'Authorization': 'Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiI5MzUzNDI1MDQwNjIzMjIxMjM5NiIsImV4cCI6MTc0NDAzOTMzMn0.5hzg-ivSSuaA9wsEz-Q_CCrEUIE6ufktx2fadU0pBG3kpIiukntfBmV6nll62u6JyTSHUtBYWXHxeGqub49FCw'
        }
        self.base_url = "https://game.dominos.com.cn"
        self.score = "f3SZOc4JCUhA3yQpAGhGrZDwC4DqFVg4lIVL6RhkLBEwzpK0MXEP8lsUdgV3V0a5VUWdTka9cavtLftv2qIuVckxuLiJ0TnewS40hB6qu4tZEnXICwb/vmuiytNvksojgXl5Bm71OCU42p5LL5EMQqY3cbnwNsqIjdxspyE7K3vpFtr9aP3u6bCPrrVwsJilZWAqjM6cd0QVo5irfzi7B5++ynTFiYo/b6fXPpHva482RLX9EMcF0w6HAomsC2iJoKuk3iiNAF2SZApQJGVigZhlZbhBCzO413fXoWlkkmCb5maYHu525YM1pRXchYlZN+aoriejBgXd6MBK6pe6Rg=="
    #@override
    def process_vars(self, info):
        self.session.headers.update(self.headers)
        self.openid = info
    #@override
    def process(self, info, _):
        self.process_vars(info)
        self.logger.info("===> 开始玩游戏")
        self.play()
        # self.logger.final(msg)
    def play(self):
        # while True:
        #     shrurl = f"{self.base_url}/bulgogi/game/sharingDone"
        #     payload2 = f"openid={self.openid}&from=1&target=0"
        #     res = self.session.post(shrurl, data=payload2)
        #     if res.ok:
        #         self.logger.info("分享成功")
        #         res_json = res.json()
        #         if res_json['statusCode'] == 0:
        #             self.logger.info("分享成功")
        #             break
        #         else:
        #             #self.logger.error(f"{res_json['errorMessage']}")
        #             if res_json['errorMessage'] == "今日分享已用完，请明日再来":
        #                 self.logger.info('ℹ️  分享已达上限')
        #                 break
        url = f"{self.base_url}/spring/v2/game/gameDone"
        payload = f"openid={self.openid}&score={self.score}"
        for a in range(5):
            res = self.session.post(url, data=payload)
            if res.ok:
                self.logger.info("抽奖成功")
                res_json = res.json()
                if res_json['statusCode'] == 0:
                    self.logger.info("抽奖成功")
                    break
                else:
                    self.logger.error(f"{res_json['errorMessage']}")
                    break
if __name__ == "__main__":
    app_name = "达美乐小游戏"
    app_env_name = "DAMEILE_CREDENTIALS"
    local_script_name = os.path.basename(__file__)
    local_version = '2025.01.18'
    run = Run(app_name=app_name,
              app_env_name=app_env_name,
              script_version=local_version)
    run.main()
