# !/usr/bin/python3
# -- coding: utf-8 --
# -------------------------------
# 哈啰出行 APP
# <AUTHOR>
# @Time 2024.09.01
# @Description
# ✨ 功能：
#     哈啰出行 签到, 积攒奖励金可换手机话费重置抵用券
# ✨ 抓包步骤：
#     打开抓包工具
#     打开 哈啰出行 APP/小程序 首页 福利中心 查看更多
#     找 api.hellobike.com/api?user 请求头中 TOKEN
# ✨ 变量示例
#     export HALUOCHUXING_TOKEN='c10dxx'，多账号换行分割
# -------------------------------
# cron "1 8 * * *" script-path=xxx.py,tag=匹配cron用
# const $ = new Env('哈啰出行 APP');
# -------------------------------
from tools.common import BaseRun
#from typing import override
import requests
import os
import json

class Run(BaseRun):
    #@override
    def init_vars(self):
        self.session = requests.Session()
        self.session.verify = False
        self.headers = {
            'content-type': 'application/json'
        }
        self.base_url = "https://api.hellobike.com"
        #获取奖励数量
        self.bounty_count_today = 0
    #@override
    def process_vars(self, info):
        self.token = info
        self.session.headers.update(self.headers)
    #@override
    def process(self, info, _):
        self.process_vars(info)
        self.logger.info("===> 签到")
        self.sign()
        msg = self.get_person_info()
        self.logger.final(msg)
    #签到
    def sign(self):
        sign_url = f'{self.base_url}/api?common.welfare.signAndRecommend'
        sign_data = json.dumps({
            "from": "h5",
            "systemCode": 62,
            "platform": 4,
            "version": "6.46.0",
            "action": "common.welfare.signAndRecommend",
            "token": self.token,
            "pointType": 1
        })
        # 签到操作
        sign_response = self.session.post(sign_url, data=sign_data).json()
        if sign_response.get("data"):
            did_sign_today = sign_response['data']['didSignToday']
            self.bounty_count_today = sign_response['data']['bountyCountToday']
            self.logger.info(f'✅ 今日签到成功 金币 +{self.bounty_count_today}' if did_sign_today else '❌ 今日未签到成功')
            return True
        else:
            self.logger.error(f'❌ {sign_response.get("msg")}')
            return False
    #获取金币、优惠券信息
    def get_person_info(self):
        point_info_url = f'{self.base_url}/api?user.taurus.pointInfo'
        point_info_data = json.dumps({
            "from": "h5",
            "systemCode": 61,
            "platform": 4,
            "version": "6.46.0",
            "action": "user.taurus.pointInfo",
            "token": self.token,
            "pointType": 1
        })
        coupon_info_url = f'{self.base_url}/api?user.wallet.coupon'
        coupon_info_data = json.dumps({
            "from": "h5",
            "systemCode": 62,
            "platform": 4,
            "version": "6.46.0",
            "action": "user.wallet.coupon",
            "token": self.token,
        })
        # 查询奖励金操作
        point_info_response = self.session.post(point_info_url, data=point_info_data).json()
        coupon_info_response = self.session.post(coupon_info_url, data=coupon_info_data).json()
        msg = ""
        if point_info_response:
            points = point_info_response['data']['points']
            msg += f"今日签到奖励金币：{self.bounty_count_today}\n"
            msg += f"账号可用奖励金币：{points}\n"
        reward = ''
        if coupon_info_response:
            for coupon in coupon_info_response['data']['couponList']:
                coupon_name = coupon['couponName']
                coupon_end_date = coupon['endDate']
                reward += f"  {coupon_name} 过期时间 {coupon_end_date}\n"
        msg += f"可用优惠券:\n{reward}\n"
        msg = msg.strip()
        return msg
        #self.logger.info(msg)


if __name__ == "__main__":
    app_name = "哈啰出行"
    app_env_name = "HALUOCHUXING_TOKEN"
    local_script_name = os.path.basename(__file__)
    local_version = '2025.01.08'
    run = Run(app_name=app_name,
              app_env_name=app_env_name,
              script_version=local_version)
    run.main()
